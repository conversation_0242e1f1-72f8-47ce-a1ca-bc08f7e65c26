[{"path": "hierarchy", "name": "Hierarchy", "meta": {"title": "体系管理", "icon": "menus-home"}, "redirect": "/hierarchy/file-management", "children": [{"path": "file-management", "name": "FileManagement", "meta": {"title": "文件管理", "icon": "menus-library"}, "redirect": "/hierarchy/file-management/internal-file-library", "children": [{"path": "internal-file-library", "name": "InternalFileLibrary", "meta": {"title": "内部文件库", "permissions": [{"name": "新增", "code": "internalFileAdd"}, {"name": "导入", "code": "internalFileImport"}, {"name": "导出", "code": "internalFileExport"}, {"name": "修订", "code": "internalFileRevision"}, {"name": "详情", "code": "internalFileDetail"}, {"name": "发放回收", "code": "internalFileDistribute"}, {"name": "作废", "code": "internalFileInvalid"}, {"name": "借阅", "code": "internalFileBorrow"}, {"name": "查阅", "code": "internalFileView"}, {"name": "下载", "code": "internalFileDownload"}, {"name": "变更记录", "code": "internalFileChangeLog"}]}, "component": "/hierarchy/file-management/internal-file-library/index.vue", "children": [{"path": "distribute-file", "name": "DistributeFile", "meta": {"title": "发放回收", "hidden": true}, "component": "/hierarchy/file-management/models/distribute-file.vue"}]}, {"path": "external-file-library", "name": "ExternalFileLibrary", "meta": {"title": "外部文件库", "permissions": [{"name": "集团库Tab", "code": "externalFileGroupTab"}, {"name": "公司库Tab", "code": "externalFileCompanyTab"}, {"name": "新增", "code": "externalFileAdd"}, {"name": "导入", "code": "externalFileImport"}, {"name": "导出", "code": "externalFileExport"}, {"name": "批量纳入", "code": "externalFileBatchInclude"}, {"name": "修订", "code": "externalFileRevision"}, {"name": "详情", "code": "externalFileDetail"}, {"name": "发放回收", "code": "externalFileDistribute"}, {"name": "纳入子公司", "code": "externalFileInclude"}, {"name": "作废", "code": "externalFileInvalid"}, {"name": "借阅", "code": "externalFileBorrow"}, {"name": "查阅", "code": "externalFileView"}, {"name": "下载", "code": "externalFileDownload"}, {"name": "变更记录", "code": "externalFileChangeLog"}]}, "component": "/hierarchy/file-management/external-file-library/index.vue", "children": [{"path": "distribute-file", "name": "DistributeFile", "meta": {"title": "发放回收", "hidden": true}, "component": "/hierarchy/file-management/models/distribute-file.vue"}]}, {"path": "book-library", "name": "BookLibrary", "meta": {"title": "书籍库", "permissions": [{"name": "新增", "code": "bookLibraryAdd"}, {"name": "导入", "code": "bookLibraryImport"}, {"name": "导出", "code": "bookLibraryExport"}, {"name": "编辑", "code": "bookLibraryEdit"}, {"name": "删除", "code": "bookLibraryDelete"}, {"name": "预览", "code": "bookLibraryPreview"}, {"name": "领用", "code": "bookLibraryReceive"}, {"name": "借用", "code": "bookLibraryBorrow"}, {"name": "领用归还", "code": "bookLibraryReceiveReturn"}, {"name": "借用归还", "code": "bookLibraryBorrowReturn"}]}, "component": "/hierarchy/file-management/book-library/index.vue"}, {"path": "issuance-application", "name": "IssuanceApplication", "meta": {"title": "发放回收和处置", "permissions": [{"name": "新增", "code": "issuanceApplicationAdd"}, {"name": "导出", "code": "issuanceApplicationExport"}, {"name": "纸质文件处置", "code": "issuanceApplicationPaperDispose"}, {"name": "发放回收详情", "code": "issuanceApplicationDistributeDetail"}, {"name": "纸质文件处置详情", "code": "issuanceApplicationPaperDetail"}, {"name": "撤销", "code": "issuanceApplicationCancel"}, {"name": "编辑", "code": "issuanceApplicationEdit"}, {"name": "回收", "code": "issuanceApplicationRecycle"}, {"name": "删除", "code": "issuanceApplicationDelete"}]}, "component": "/hierarchy/file-management/issuance-application/index.vue"}, {"path": "borrowing-application", "name": "BorrowingApplication", "meta": {"title": "借阅和交还"}, "component": "/hierarchy/file-management/borrowing-application/index.vue"}, {"path": "invalid-application", "name": "InvalidApplication", "meta": {"title": "作废申请", "permissions": [{"name": "新增", "code": "internalInvalidFileAdd"}, {"name": "导出", "code": "internalInvalidFileExport"}, {"name": "撤销", "code": "internalInvalidFileRevoke"}, {"name": "详情", "code": "internalInvalidFileDetail"}, {"name": "删除", "code": "internalInvalidFileDelete"}]}, "component": "/hierarchy/file-management/invalid-application/index.vue"}, {"path": "internal-invalid-file-library", "name": "InternalInvalidFileLibrary", "meta": {"title": "内部作废文件库", "permissions": [{"name": "导出", "code": "internalInvalidFileExport"}, {"name": "借阅", "code": "internalInvalidFileBorrow"}, {"name": "作废详情", "code": "internalInvalidFileDetail"}]}, "component": "/hierarchy/file-management/internal-invalid-file-library/index.vue"}, {"path": "external-invalid-file-library", "name": "ExternalInvalidFileLibrary", "meta": {"title": "外部作废文件库", "permissions": [{"name": "集团库Tab", "code": "externalInvalidFileGroupTab"}, {"name": "公司库Tab", "code": "externalInvalidFileCompanyTab"}, {"name": "导出", "code": "externalInvalidFileExport"}, {"name": "借阅", "code": "externalInvalidFileBorrow"}, {"name": "作废详情", "code": "externalInvalidFileDetail"}]}, "component": "/hierarchy/file-management/external-invalid-file-library/index.vue"}]}, {"path": "institution-management", "name": "InstitutionManagement", "meta": {"title": "机构管理", "icon": "menus-orgin"}, "component": "/hierarchy/institution-management/index.vue"}]}, {"path": "system", "name": "System", "meta": {"title": "系统管理"}, "redirect": "/system/organization", "children": [{"path": "organization", "name": "Organization", "meta": {"title": "组织架构", "icon": "menus-setting", "permissions": [{"name": "新建单位", "code": "addOrganization"}, {"name": "新建子单位", "code": "addSubOrganization"}, {"name": "新增部门", "code": "addDepartment"}, {"name": "编辑组织架构", "code": "editOrganization"}, {"name": "设置负责人", "code": "setUpLeaders"}, {"name": "删除组织架构", "code": "deleteDepartment"}, {"name": "新增用户", "code": "addUser"}, {"name": "绑定用户", "code": "bindUser"}, {"name": "人员排序", "code": "userSort"}, {"name": "编辑用户", "code": "editUser"}, {"name": "删除用户", "code": "deleteUser"}, {"name": "批量密码修改", "code": "batchUpdatePassword"}, {"name": "关闭模态框", "code": "afterLeave"}]}, "component": "/system/organization/index.vue"}, {"path": "online-user", "name": "OnlineUser", "meta": {"title": "在线用户", "icon": "menus-user", "permissions": [{"name": "强制登出", "code": "onlineUserLogout"}]}, "component": "/system/online-user/index.vue"}, {"path": "position", "name": "Position", "meta": {"title": "岗位管理", "icon": "menus-position", "permissions": [{"name": "新增岗位", "code": "addPosition"}, {"name": "编辑岗位", "code": "editPosition"}, {"name": "删除岗位", "code": "deletePosition"}]}, "component": "/system/position/index.vue"}, {"path": "menu", "name": "<PERSON><PERSON>", "meta": {"title": "菜单管理", "icon": "menus-menu", "permissions": [{"name": "新增菜单", "code": "addMenu"}, {"name": "编辑菜单", "code": "editMenu"}, {"name": "删除菜单", "code": "deleteMenu"}, {"name": "按钮设置", "code": "buttonSet"}]}, "component": "/system/menu/index.vue"}, {"path": "system-log", "name": "SystemLog", "meta": {"title": "日志管理", "icon": "menus-note"}, "redirect": "/system/system-log/login-log", "children": [{"path": "login-log", "name": "loginLog", "meta": {"title": "登录日志"}, "component": "/system/system-log/index.vue"}, {"path": "operation-log", "name": "operationLog", "meta": {"title": "操作日志"}, "component": "/system/system-log/index.vue"}]}, {"path": "role", "name": "Role", "meta": {"title": "角色管理", "icon": "menus-role", "permissions": [{"name": "新增角色", "code": "addRole"}, {"name": "编辑角色", "code": "editRole"}, {"name": "删除角色", "code": "deleteRole"}, {"name": "绑定人员", "code": "bindUser"}, {"name": "菜单权限", "code": "menuAuth"}]}, "component": "/system/role/index.vue"}, {"path": "dictionary", "name": "Dictionary", "meta": {"title": "字典管理", "icon": "menus-dict", "permissions": [{"name": "新增字典", "code": "dictionaryAdd"}, {"name": "编辑字典", "code": "dictionaryEdit"}, {"name": "查看字典", "code": "dictionaryView"}, {"name": "新增字典节点", "code": "dictionaryNodeAdd"}, {"name": "编辑字典节点", "code": "dictionaryNodeEdit"}, {"name": "删除字典节点", "code": "dictionaryNodeDelete"}, {"name": "全部展开节点", "code": "dictionaryNodeExpendAll"}, {"name": "重置字典节点", "code": "dictionaryNodeReset"}]}, "component": "/system/dictionary/index.vue"}, {"path": "more-help", "name": "MoreHelp", "meta": {"title": "更多帮助", "icon": "menus-more", "permissions": [{"name": "新增帮助", "code": "addMoreHelp"}, {"name": "编辑帮助", "code": "editMoreHelp"}, {"name": "删除帮助", "code": "deleteMoreHelp"}]}, "component": "/system/more-help/index.vue"}, {"path": "signature", "name": "Signature", "meta": {"title": "签名管理", "hidden": true, "permissions": [{"name": "签名配置", "code": "signatureConfig"}]}, "component": "/system/signature/index.vue"}, {"path": "system-dict", "name": "SystemDict", "meta": {"title": "系统字典", "icon": "menus-library", "permissions": [{"name": "新增系统字典", "code": "addSystemDict"}, {"name": "编辑系统字典", "code": "editSystemDict"}, {"name": "删除系统字典", "code": "deleteSystemDict"}]}, "component": "/system/system-dict/index.vue"}, {"path": "tenant", "name": "Tenant", "meta": {"title": "租户管理", "icon": "menus-process1", "hidden": true, "isActive": false, "permissions": [{"name": "用户列表", "code": "userList"}, {"name": "新增用户", "code": "addUser"}, {"name": "编辑用户", "code": "editUser"}, {"name": "删除用户", "code": "deleteUser"}, {"name": "绑定用户", "code": "bindUser"}, {"name": "新增租户", "code": "add<PERSON><PERSON>t"}, {"name": "编辑租户", "code": "edit<PERSON><PERSON><PERSON>"}, {"name": "删除租户", "code": "deleteTenant"}]}, "component": "/system/tenant/index.vue"}]}, {"path": "approval", "name": "Approval", "meta": {"title": "审批管理"}, "redirect": "/approval/approval-process", "children": [{"path": "approval-preset-management", "name": "approval-preset-management", "meta": {"title": "流程模版管理", "icon": "menus-process2", "permissions": [{"name": "新增流程", "code": "approvalProcessPresetAdd"}, {"name": "编辑流程", "code": "approvalProcessPresetEdit"}, {"name": "分发流程", "code": "approvalProcessPresetDispense"}]}, "component": "/approval/approval-process-preset.vue"}, {"path": "approval-process-management", "name": "approval-process-management", "meta": {"title": "审批流程管理", "icon": "menus-process7", "permissions": [{"name": "详情", "code": "approvalProcessManagementView"}, {"name": "编辑", "code": "approvalProcessManagementEdit"}, {"name": "发布记录", "code": "approvalProcessManagementPublish"}]}, "component": "/approval/approval-process-management.vue"}, {"path": "approval-process", "name": "ApprovalProcess", "meta": {"title": "审批待办", "icon": "menus-process6", "permissions": [{"name": "审批待办处理", "code": "approvalPendingProcess"}, {"name": "审批待办查看", "code": "approvalPendingView"}, {"name": "审批待办撤回", "code": "approvalPendingRecall"}]}, "component": "/approval/index.vue"}, {"path": "approval-process-monitoring", "name": "approval-process-monitoring", "meta": {"title": "流程监控", "icon": "menus-process5", "permissions": [{"name": "查看", "code": "approvalProcessMonitoringView"}, {"name": "节点管理", "code": "approvalProcessMonitoringNodeView"}]}, "component": "/approval/approval-process-monitoring.vue"}]}, {"path": "entry", "name": "Entry", "meta": {"title": "首页", "isFullPage": true}, "component": "/entry/index.vue"}]