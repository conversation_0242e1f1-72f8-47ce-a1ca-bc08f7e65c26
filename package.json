{"name": "zhongyi-lims", "version": "0.0.1", "scripts": {"dev": "npm run build-pre && vite", "build": "npm run lint && vite build && npm run compress:dist", "build:custom": "npm run lint && vite build && npm run compress:dist custom", "build-pre": "tsnd  -P ./src/utils/scripts/tsconfig-build.json src/utils/scripts/build-pre.ts --run-preset", "serve": "vite preview --host", "lint": "npm run build-pre && eslint src --ext .ts,.tsx,.vue", "prettier": "prettier --write .", "prepare": "husky install", "compress:dist": "node scripts/compress.js", "push_dev_serve:150": "npm run build && node scripts/push_dev_serve.js *************** /data/projects/frontend", "move_tag_to_latest": "sh scripts/move_tag_to_latest.sh", "auto-build": "node scripts/generate-auto-import-config.js && vite build && npm run compress:dist"}, "private": true, "dependencies": {"@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.13.0", "@vueuse/core": "^9.13.0", "@vxe-ui/plugin-render-naive": "4.0.11", "dayjs": "^1.11.13", "jsoneditor": "^10.2.0", "lodash": "^4.17.21", "md5": "^2.3.0", "mescroll.js": "^1.4.2", "motion-v": "^1.1.1", "naive-ui": "git+http://***************/zj-front-end/wisdom-naive-release.git", "pinia": "^2.3.1", "resize-observer": "^1.0.4", "screenfull": "^6.0.2", "smooth-signature": "^1.1.0", "vant": "^4.4.1", "vconsole-hide": "^0.0.8", "vue": "^3.5.14", "vue-clipboard3": "^2.0.0", "vue-router": "^4.5.1", "vue3-json-viewer": "^2.4.0", "vxe-pc-ui": "4.6.18", "vxe-table": "4.13.36", "wp-preprocessor": "git+http://***************/zj-front-end/wp-preprocessor-release.git", "wp-request": "git+http://***************/zj-front-end/wp-request-release.git", "wp-utils": "git+http://***************/zj-front-end/wp-utils-release.git"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@inspira-ui/plugins": "^0.0.1", "@originjs/vite-plugin-commonjs": "^1.0.1", "@types/adm-zip": "^0.5.7", "@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@types/jsoneditor": "^9.9.5", "@types/lodash": "^4.17.17", "@types/md5": "^2.3.2", "@types/mockjs": "^1.0.10", "@types/node": "^18.15.3", "@types/three": "^0.176.0", "@types/vue3-json-viewer": "^2.2.0", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/runtime-core": "^3.2.45", "adm-zip": "^0.5.16", "autoprefixer": "^10.4.21", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^8.57.1", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "eslint-plugin-vue3-jsx": "^0.0.3-beta.3", "fs-extra": "^11.3.0", "glob": "^11.0.3", "husky": "^8.0.3", "less": "^4.3.0", "lint-staged": "^13.2.0", "mockjs": "^1.1.0", "node-ssh": "^13.2.1", "path": "^0.12.7", "prettier": "^2.8.4", "tailwind-merge": "^3.3.0", "tailwindcss": "3", "tailwindcss-animate": "^1.0.7", "terser": "^5.14.2", "ts-node-dev": "^2.0.0", "typescript": "~5.0.4", "unocss": "^0.53.1", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.24.1", "vite": "^4.5.14", "vite-plugin-fz": "git+http://***************/zj-front-end/vite-plugin-fz-release.git", "vite-plugin-html-security": "^0.0.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.6", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vitejs-get-vue-ref": "^0.0.2", "vitejs-plugin-api-auto-import": "^0.0.24"}, "license": "MIT"}