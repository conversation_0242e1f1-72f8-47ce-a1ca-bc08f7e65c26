## 依赖安装

-   使用 `yarn` 命令进行安装
-   使用 `yarn upgrade xxx` 命令进行某个依赖包更新

## 项目工具可视化页面

-   http://localhost:5173/#/common-tools 【端口需要根据实际项目中运行的端口保持一致】

## unocss 查询

-   文档: https://unocss.dev/
-   查询: https://unocss.dev/interactive/

## loading 资源

-   [loading 资源网站](https://loading.io/spinner/)

## mescroll.js

-   [mescroll.js 文档](http://www.mescroll.com/api.html)

```js
/**
 * 白名单，用于排除不需要登录的页面(示例)
 */
whiteList: [
    'login',
    (to) => {
        return to.path === '/large-screen/home';
    },
],
```

## 代码仓库版本和分支管理策略

### 一、版本号命名规范

采用语义化版本控制（Semantic Versioning）：

1. 格式：vMAJOR.MINOR.PATCH
2. 规则：
    - MAJOR：向后不兼容的变更时递增。
    - MINOR：添加功能或修改现有功能但保持兼容时递增。
    - PATCH：修复 bug 时递增。

### 二、分支管理策略（采用 Git Flow）

#### 主分支

-   main：稳定版本，仅通过发布流程合并。

#### 发布分支

-   release-：准备发布时创建，命名如 release-v1.1.0。
-   用法：从 main 分支创建，用于测试和修复，完成后合并到 main，并打标签 v1.1.0。

#### 开发分支

-   feature-：开发新功能时创建，命名如 feature-authentication-nickname。
-   hotfix- : 修复 bug 的分支，命名如 hotfix-add-user-nickname
-   refactor-：重构代码的分支，命名如 refactor-add-user-nickname
-   用法：从 main 分支创建，完成后合并回 release。
