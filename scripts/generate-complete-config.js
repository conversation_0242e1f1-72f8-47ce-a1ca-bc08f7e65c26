const path = require('path');
const { execSync } = require('child_process');
const fs = require('fs');

/**
 * 完整的配置生成脚本
 * 解决 build-pre 脚本生成的依赖项没有正确添加到 .eslintrc-auto-import.json 的问题
 */
async function generateCompleteConfig() {
    try {
        console.log('🚀 开始生成完整的配置文件...');
        
        // 步骤1: 确保先运行 build-pre 脚本
        console.log('📦 步骤1: 运行 build-pre 脚本生成业务预设...');
        try {
            execSync('npm run build-pre', { stdio: 'pipe', cwd: process.cwd() });
            console.log('✅ build-pre 脚本执行成功');
        } catch (e) {
            console.error('❌ build-pre 脚本执行失败:', e.message);
            // 不要抛出错误，继续执行
            console.log('⚠️ 继续执行，但可能会缺少一些业务预设');
        }
        
        // 步骤2: 等待一下确保文件写入完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 步骤3: 验证 auto-import-business-preset.json 文件是否存在
        const businessPresetPath = path.resolve(process.cwd(), 'auto-import-business-preset.json');
        if (!fs.existsSync(businessPresetPath)) {
            throw new Error('auto-import-business-preset.json 文件不存在');
        }
        
        console.log('📦 步骤2: 导入所需模块...');
        const AutoImport = (await import('unplugin-auto-import/vite')).default;
        const Components = (await import('unplugin-vue-components/vite')).default;
        const { NaiveUiResolver, VantResolver } = await import('unplugin-vue-components/resolvers');
        
        // 导入 AutoApi 插件
        let AutoApi;
        try {
            const autoApiModule = await import('vitejs-plugin-api-auto-import');
            AutoApi = autoApiModule.default || autoApiModule;
        } catch (e) {
            console.warn('⚠️ 无法加载 AutoApi 插件:', e.message);
            AutoApi = null;
        }
        
        // 导入自定义解析器和预设
        let AutoImportResolvers, AutoComponentsResolvers, AutoImportBusinessPreset;
        try {
            // 清除模块缓存，确保重新加载
            const resolversPath = path.resolve(process.cwd(), 'src/utils/vite/auto-resolvers.ts');
            delete require.cache[resolversPath];
            
            const resolvers = await import('../src/utils/vite/auto-resolvers.ts');
            AutoImportResolvers = resolvers.AutoImportResolvers;
            AutoComponentsResolvers = resolvers.AutoComponentsResolvers;
            AutoImportBusinessPreset = resolvers.AutoImportBusinessPreset;
            console.log('✅ 自定义解析器加载成功');
        } catch (e) {
            console.error('❌ 无法加载自定义解析器:', e.message);
            throw e;
        }

        // 导入预设模块
        let preprocessorPreset, requestPreset, remPreset;
        try {
            preprocessorPreset = (await import('wp-preprocessor/dist/preset')).default;
            requestPreset = (await import('wp-request/dist/preset')).default;
            const { remPreset: rp } = await import('vite-plugin-fz');
            remPreset = rp;
            console.log('✅ 预设模块加载成功');
        } catch (e) {
            console.warn('⚠️ 无法加载预设模块:', e.message);
            preprocessorPreset = {};
            requestPreset = {};
            remPreset = {};
        }

        // 重新加载业务预设
        console.log('📦 步骤3: 加载业务预设...');
        const businessPreset = AutoImportBusinessPreset();
        console.log(`业务预设内容: ${Object.keys(businessPreset).length > 0 ? `已加载 ${Object.keys(businessPreset).length} 个预设` : '为空'}`);
        
        // 打印详细信息用于调试
        console.log('预设详情:');
        console.log('- preprocessorPreset keys:', Object.keys(preprocessorPreset));
        console.log('- requestPreset keys:', Object.keys(requestPreset));
        console.log('- remPreset keys:', Object.keys(remPreset));
        console.log('- businessPreset keys:', Object.keys(businessPreset));
        
        // 步骤4: 创建 AutoImport 插件实例
        console.log('📦 步骤4: 创建 AutoImport 插件实例...');
        const autoImportPlugin = AutoImport({
            include: [
                /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
                /\.vue$/,
                /\.vue\?vue/, // .vue
                /\.md$/ // .md
            ],
            imports: [
                preprocessorPreset,
                requestPreset,
                remPreset,
                {
                    '@/api': ['api']
                },
                businessPreset,
                // presets
                'vue',
                'vue-router',
                '@vueuse/core',
                'pinia'
            ],
            eslintrc: {
                enabled: true,
                filepath: './.eslintrc-auto-import.json'
            },
            resolvers: [AutoImportResolvers()],
            dts: true,
            dirs: []
        });

        // 步骤5: 创建 Components 插件实例
        console.log('📦 步骤5: 创建 Components 插件实例...');
        const componentsPlugin = Components({
            resolvers: [NaiveUiResolver(), VantResolver(), AutoComponentsResolvers()]
        });

        // 步骤6: 创建 AutoApi 插件实例
        console.log('📦 步骤6: 创建 AutoApi 插件实例...');
        const autoApiPlugins = [];
        if (AutoApi && typeof AutoApi === 'function') {
            autoApiPlugins.push(
                AutoApi({
                    resolveAliasName: '@/api/apis',
                    dir: 'src/api/apis'
                }),
                AutoApi({
                    name: '$alert',
                    resolveAliasName: '@/alert',
                    dir: 'src/alert'
                }),
                AutoApi({
                    name: '$utils',
                    resolveAliasName: '@/utils/utils',
                    dir: 'src/utils/utils'
                }),
                AutoApi({
                    name: '$hooks',
                    resolveAliasName: '@/hooks',
                    dir: 'src/hooks'
                }),
                AutoApi({
                    name: '$datas',
                    resolveAliasName: '@/data',
                    dir: 'src/data'
                })
            );
            console.log(`✅ 创建了 ${autoApiPlugins.length} 个 AutoApi 插件实例`);
        }

        // 步骤7: 模拟 Vite 构建上下文
        console.log('📦 步骤7: 模拟 Vite 构建上下文...');
        const mockContext = {
            resolve: (id) => ({ id }),
            load: () => null,
            transform: () => null,
            meta: {
                rollupVersion: '3.0.0'
            }
        };

        const mockConfig = {
            command: 'build',
            mode: 'production',
            root: process.cwd(),
            plugins: [],
            build: {
                outDir: 'dist_management'
            },
            resolve: {
                alias: {
                    '@': path.resolve(process.cwd(), 'src')
                }
            },
            optimizeDeps: {
                include: [],
                exclude: []
            }
        };

        // 步骤8: 执行插件生成配置文件
        console.log('📦 步骤8: 执行插件生成配置文件...');
        
        // 处理 AutoImport 插件
        try {
            if (autoImportPlugin.configResolved) {
                await autoImportPlugin.configResolved.call(mockContext, mockConfig);
            }
            if (autoImportPlugin.buildStart) {
                await autoImportPlugin.buildStart.call(mockContext, {});
            }
            console.log('✅ AutoImport 配置生成成功');
        } catch (error) {
            console.error('❌ AutoImport 生成失败:', error.message);
            throw error;
        }

        // 处理 Components 插件
        try {
            if (componentsPlugin.configResolved) {
                await componentsPlugin.configResolved.call(mockContext, mockConfig);
            }
            if (componentsPlugin.buildStart) {
                await componentsPlugin.buildStart.call(mockContext, {});
            }
            console.log('✅ Components 配置生成成功');
        } catch (error) {
            console.warn('⚠️ Components 生成失败:', error.message);
        }

        // 处理 AutoApi 插件
        for (const [index, autoApiPlugin] of autoApiPlugins.entries()) {
            try {
                if (autoApiPlugin.configResolved) {
                    await autoApiPlugin.configResolved.call(mockContext, mockConfig);
                }
                if (autoApiPlugin.buildStart) {
                    await autoApiPlugin.buildStart.call(mockContext, {});
                }
                console.log(`✅ AutoApi 插件 ${index + 1} 配置生成成功`);
            } catch (error) {
                console.warn(`⚠️ AutoApi 插件 ${index + 1} 生成失败:`, error.message);
            }
        }
        
        // 步骤9: 验证生成的文件
        console.log('📦 步骤9: 验证生成的文件...');
        const filesToCheck = [
            '.eslintrc-auto-import.json',
            'components.d.ts',
            'auto-imports.d.ts'
        ];
        
        for (const file of filesToCheck) {
            const filePath = path.resolve(process.cwd(), file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                console.log(`✅ ${file} 存在 (${stats.size} bytes)`);
                
                // 特别检查 .eslintrc-auto-import.json 的内容
                if (file === '.eslintrc-auto-import.json') {
                    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                    const globalCount = Object.keys(content.globals || {}).length;
                    console.log(`   包含 ${globalCount} 个全局变量`);
                    
                    // 检查是否包含业务组件
                    const hasBusinessComponents = Object.keys(content.globals || {}).some(key => 
                        key.startsWith('BS') || key.startsWith('Bs') || key.startsWith('bs')
                    );
                    console.log(`   包含业务组件: ${hasBusinessComponents ? '是' : '否'}`);
                    
                    // 检查是否包含预设模块
                    const hasPresetModules = Object.keys(content.globals || {}).some(key => 
                        ['$http', 'request', 'preprocessor', 'fh', 'fz'].includes(key)
                    );
                    console.log(`   包含预设模块: ${hasPresetModules ? '是' : '否'}`);
                }
            } else {
                console.log(`⚠️ ${file} 不存在`);
            }
        }
        
        console.log('🎉 完整配置文件生成完成！');
        
    } catch (error) {
        console.error('❌ 生成失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    generateCompleteConfig();
}

module.exports = { generateCompleteConfig };
