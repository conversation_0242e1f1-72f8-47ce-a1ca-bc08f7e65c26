const path = require('path');
const { execSync } = require('child_process');
const fs = require('fs');

/**
 * 确保完整配置生成的脚本
 * 这个脚本会检查当前的配置文件是否完整，如果不完整则重新生成
 */
async function ensureCompleteConfig() {
    try {
        console.log('🔍 检查配置文件完整性...');
        
        const eslintrcPath = path.resolve(process.cwd(), '.eslintrc-auto-import.json');
        let needsRegeneration = false;
        
        // 检查 .eslintrc-auto-import.json 文件
        if (!fs.existsSync(eslintrcPath)) {
            console.log('❌ .eslintrc-auto-import.json 文件不存在');
            needsRegeneration = true;
        } else {
            const eslintrcContent = JSON.parse(fs.readFileSync(eslintrcPath, 'utf8'));
            const globals = eslintrcContent.globals || {};
            const globalCount = Object.keys(globals).length;
            
            console.log(`📊 当前 .eslintrc-auto-import.json 包含 ${globalCount} 个全局变量`);
            
            // 检查关键指标
            const hasBusinessComponents = Object.keys(globals).some(key => 
                key.startsWith('BS') || key.startsWith('Bs') || key.startsWith('bs')
            );
            const hasPresetModules = Object.keys(globals).some(key => 
                ['$http', 'request', 'preprocessor', 'fh', 'fz'].includes(key)
            );
            const hasCustomHooks = Object.keys(globals).some(key => 
                key.endsWith('Hooks') || key.endsWith('hooks')
            );
            const hasVueCore = Object.keys(globals).some(key => 
                ['ref', 'reactive', 'computed', 'watch'].includes(key)
            );
            
            console.log(`   业务组件: ${hasBusinessComponents ? '✅' : '❌'}`);
            console.log(`   预设模块: ${hasPresetModules ? '✅' : '❌'}`);
            console.log(`   自定义hooks: ${hasCustomHooks ? '✅' : '❌'}`);
            console.log(`   Vue核心: ${hasVueCore ? '✅' : '❌'}`);
            
            // 如果缺少关键组件或全局变量数量太少，则需要重新生成
            if (!hasBusinessComponents || !hasPresetModules || !hasCustomHooks || !hasVueCore || globalCount < 200) {
                console.log('⚠️ 配置文件不完整，需要重新生成');
                needsRegeneration = true;
            } else {
                console.log('✅ 配置文件完整');
            }
        }
        
        if (needsRegeneration) {
            console.log('🔄 重新生成配置文件...');
            
            // 步骤1: 运行 build-pre 脚本
            console.log('📦 运行 build-pre 脚本...');
            try {
                execSync('npm run build-pre', { stdio: 'pipe', cwd: process.cwd() });
                console.log('✅ build-pre 脚本执行成功');
            } catch (e) {
                console.warn('⚠️ build-pre 脚本执行失败，继续执行');
            }
            
            // 步骤2: 运行 generate-eslint 脚本
            console.log('📦 运行 generate-eslint 脚本...');
            try {
                execSync('npm run generate-eslint', { stdio: 'pipe', cwd: process.cwd() });
                console.log('✅ generate-eslint 脚本执行成功');
            } catch (e) {
                console.warn('⚠️ generate-eslint 脚本执行失败');
            }
            
            // 步骤3: 验证结果
            if (fs.existsSync(eslintrcPath)) {
                const newContent = JSON.parse(fs.readFileSync(eslintrcPath, 'utf8'));
                const newGlobalCount = Object.keys(newContent.globals || {}).length;
                console.log(`✅ 重新生成完成，包含 ${newGlobalCount} 个全局变量`);
            } else {
                console.log('❌ 重新生成失败');
            }
        }
        
        // 检查其他重要文件
        const otherFiles = [
            'components.d.ts',
            'auto-imports.d.ts',
            'auto-import-business-preset.json'
        ];
        
        console.log('📄 检查其他配置文件:');
        for (const file of otherFiles) {
            const filePath = path.resolve(process.cwd(), file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                console.log(`   ✅ ${file} (${stats.size} bytes)`);
            } else {
                console.log(`   ❌ ${file} 不存在`);
            }
        }
        
        console.log('🎉 配置检查完成！');
        
    } catch (error) {
        console.error('❌ 配置检查失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    ensureCompleteConfig();
}

module.exports = { ensureCompleteConfig };
