const path = require('path');

/**
 * 统一的自动导入配置生成脚本
 * 替代 vite 命令执行时的 .eslintrc-auto-import.json 和 components.d.ts 文件生成过程
 *
 * 注意：运行此脚本前需要先执行 npm run build-pre 生成业务预设
 *
 * 执行步骤：
 * 1. 生成 .eslintrc-auto-import.json 文件
 * 2. 生成 components.d.ts 文件
 * 3. 生成 auto-imports.d.ts 文件
 */
async function generateAutoImportConfig() {
    try {
        console.log('🚀 开始生成自动导入配置文件...');

        // 步骤1: 导入所需模块
        console.log('📦 步骤1: 导入所需模块...');
        const AutoImport = (await import('unplugin-auto-import/vite')).default;
        const Components = (await import('unplugin-vue-components/vite')).default;
        const { NaiveUiResolver, VantResolver } = await import('unplugin-vue-components/resolvers');
        
        // 导入 AutoApi 插件
        let AutoApi;
        try {
            const autoApiModule = await import('vitejs-plugin-api-auto-import');
            AutoApi = autoApiModule.default || autoApiModule;
        } catch (e) {
            console.warn('⚠️ 无法加载 AutoApi 插件:', e.message);
            AutoApi = null;
        }
        
        // 导入自定义解析器和预设
        let AutoImportResolvers, AutoComponentsResolvers, AutoImportBusinessPreset;
        try {
            const resolvers = await import('../src/utils/vite/auto-resolvers.ts');
            AutoImportResolvers = resolvers.AutoImportResolvers;
            AutoComponentsResolvers = resolvers.AutoComponentsResolvers;
            AutoImportBusinessPreset = resolvers.AutoImportBusinessPreset;
            console.log('✅ 自定义解析器加载成功');
        } catch (e) {
            console.error('❌ 无法加载自定义解析器:', e.message);
            throw e;
        }

        // 导入预设模块
        let preprocessorPreset, requestPreset, remPreset;
        try {
            preprocessorPreset = (await import('wp-preprocessor/dist/preset')).default;
            requestPreset = (await import('wp-request/dist/preset')).default;
            const { remPreset: rp } = await import('vite-plugin-fz');
            remPreset = rp;
            console.log('✅ 预设模块加载成功');
        } catch (e) {
            console.warn('⚠️ 无法加载预设模块，使用空对象:', e.message);
            preprocessorPreset = {};
            requestPreset = {};
            remPreset = {};
        }

        // 加载业务预设
        const businessPreset = AutoImportBusinessPreset();
        console.log(`📊 业务预设: ${Object.keys(businessPreset).length > 0 ? `已加载 ${Object.keys(businessPreset).length} 个预设` : '为空'}`);
        
        // 步骤2: 创建 AutoImport 插件实例
        console.log('📦 步骤2: 创建 AutoImport 插件实例...');
        const autoImportPlugin = AutoImport({
            include: [
                /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
                /\.vue$/,
                /\.vue\?vue/, // .vue
                /\.md$/ // .md
            ],
            imports: [
                preprocessorPreset,
                requestPreset,
                remPreset,
                {
                    '@/api': ['api']
                },
                businessPreset,
                // presets
                'vue',
                'vue-router',
                '@vueuse/core',
                'pinia'
            ],
            eslintrc: {
                enabled: true,
                filepath: './.eslintrc-auto-import.json'
            },
            resolvers: [AutoImportResolvers()],
            dts: true,
            dirs: []
        });

        // 步骤3: 创建 Components 插件实例
        console.log('📦 步骤3: 创建 Components 插件实例...');
        const componentsPlugin = Components({
            resolvers: [NaiveUiResolver(), VantResolver(), AutoComponentsResolvers()]
        });

        // 步骤5: 创建 AutoApi 插件实例
        const autoApiPlugins = [];
        if (AutoApi && typeof AutoApi === 'function') {
            autoApiPlugins.push(
                AutoApi({
                    resolveAliasName: '@/api/apis',
                    dir: 'src/api/apis'
                }),
                AutoApi({
                    name: '$alert',
                    resolveAliasName: '@/alert',
                    dir: 'src/alert'
                }),
                AutoApi({
                    name: '$utils',
                    resolveAliasName: '@/utils/utils',
                    dir: 'src/utils/utils'
                }),
                AutoApi({
                    name: '$hooks',
                    resolveAliasName: '@/hooks',
                    dir: 'src/hooks'
                }),
                AutoApi({
                    name: '$datas',
                    resolveAliasName: '@/data',
                    dir: 'src/data'
                })
            );
            console.log(`📦 步骤4: 创建了 ${autoApiPlugins.length} 个 AutoApi 插件实例`);
        }

        // 步骤5: 模拟 Vite 构建上下文
        console.log('📦 步骤5: 模拟 Vite 构建上下文并生成配置文件...');
        const mockContext = {
            resolve: (id) => ({ id }),
            load: () => null,
            transform: () => null,
            meta: {
                rollupVersion: '3.0.0'
            }
        };

        const mockConfig = {
            command: 'build',
            mode: 'production',
            root: process.cwd(),
            plugins: [],
            build: {
                outDir: 'dist_management'
            },
            resolve: {
                alias: {
                    '@': path.resolve(process.cwd(), 'src')
                }
            },
            optimizeDeps: {
                include: [],
                exclude: []
            }
        };

        // 执行插件生成配置文件
        try {
            if (autoImportPlugin.configResolved) {
                await autoImportPlugin.configResolved.call(mockContext, mockConfig);
            }
            if (autoImportPlugin.buildStart) {
                await autoImportPlugin.buildStart.call(mockContext, {});
            }
            console.log('✅ AutoImport 配置生成成功');
        } catch (error) {
            console.error('❌ AutoImport 生成失败:', error.message);
            throw error;
        }

        try {
            if (componentsPlugin.configResolved) {
                await componentsPlugin.configResolved.call(mockContext, mockConfig);
            }
            if (componentsPlugin.buildStart) {
                await componentsPlugin.buildStart.call(mockContext, {});
            }
            console.log('✅ Components 配置生成成功');
        } catch (error) {
            console.warn('⚠️ Components 生成失败:', error.message);
        }

        for (const [index, autoApiPlugin] of autoApiPlugins.entries()) {
            try {
                if (autoApiPlugin.configResolved) {
                    await autoApiPlugin.configResolved.call(mockContext, mockConfig);
                }
                if (autoApiPlugin.buildStart) {
                    await autoApiPlugin.buildStart.call(mockContext, {});
                }
                console.log(`✅ AutoApi 插件 ${index + 1} 配置生成成功`);
            } catch (error) {
                console.warn(`⚠️ AutoApi 插件 ${index + 1} 生成失败:`, error.message);
            }
        }
        
        console.log('🎉 所有配置文件生成完成！');
        
    } catch (error) {
        console.error('❌ 生成失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    generateAutoImportConfig();
}

module.exports = { generateAutoImportConfig };
