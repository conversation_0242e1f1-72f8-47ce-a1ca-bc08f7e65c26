const path = require('path');
const fs = require('fs');

/**
 * 测试配置生成功能
 */
async function testConfigGeneration() {
    try {
        console.log('🧪 测试配置生成功能...');
        
        // 检查 auto-import-business-preset.json 是否存在
        const businessPresetPath = path.resolve(process.cwd(), 'auto-import-business-preset.json');
        if (fs.existsSync(businessPresetPath)) {
            const businessPresetContent = JSON.parse(fs.readFileSync(businessPresetPath, 'utf8'));
            console.log('✅ auto-import-business-preset.json 存在');
            console.log(`   包含 ${Object.keys(businessPresetContent).length} 个业务预设`);
        } else {
            console.log('❌ auto-import-business-preset.json 不存在');
        }
        
        // 导入模块测试
        console.log('📦 测试模块导入...');
        
        try {
            const AutoImport = (await import('unplugin-auto-import/vite')).default;
            console.log('✅ AutoImport 模块导入成功');
        } catch (e) {
            console.error('❌ AutoImport 模块导入失败:', e.message);
        }
        
        try {
            const { AutoImportBusinessPreset } = await import('../src/utils/vite/auto-resolvers.ts');
            const businessPreset = AutoImportBusinessPreset();
            console.log('✅ AutoImportBusinessPreset 导入成功');
            console.log(`   业务预设包含 ${Object.keys(businessPreset).length} 个项目`);
            
            // 打印前几个业务预设项目
            const keys = Object.keys(businessPreset).slice(0, 3);
            keys.forEach(key => {
                console.log(`   - ${key}: ${businessPreset[key].length} 个导入`);
            });
        } catch (e) {
            console.error('❌ AutoImportBusinessPreset 导入失败:', e.message);
        }
        
        try {
            const preprocessorPreset = (await import('wp-preprocessor/dist/preset')).default;
            console.log('✅ preprocessorPreset 导入成功');
            console.log(`   包含 ${Object.keys(preprocessorPreset).length} 个预设`);
        } catch (e) {
            console.error('❌ preprocessorPreset 导入失败:', e.message);
        }
        
        try {
            const requestPreset = (await import('wp-request/dist/preset')).default;
            console.log('✅ requestPreset 导入成功');
            console.log(`   包含 ${Object.keys(requestPreset).length} 个预设`);
        } catch (e) {
            console.error('❌ requestPreset 导入失败:', e.message);
        }
        
        try {
            const { remPreset } = await import('vite-plugin-fz');
            console.log('✅ remPreset 导入成功');
            console.log(`   包含 ${Object.keys(remPreset).length} 个预设`);
        } catch (e) {
            console.error('❌ remPreset 导入失败:', e.message);
        }
        
        // 检查现有的 .eslintrc-auto-import.json 文件
        const eslintrcPath = path.resolve(process.cwd(), '.eslintrc-auto-import.json');
        if (fs.existsSync(eslintrcPath)) {
            const eslintrcContent = JSON.parse(fs.readFileSync(eslintrcPath, 'utf8'));
            console.log('📄 当前 .eslintrc-auto-import.json 文件分析:');
            console.log(`   包含 ${Object.keys(eslintrcContent.globals || {}).length} 个全局变量`);
            
            // 检查业务组件
            const businessComponents = Object.keys(eslintrcContent.globals || {}).filter(key => 
                key.startsWith('BS') || key.startsWith('Bs') || key.startsWith('bs')
            );
            console.log(`   业务组件: ${businessComponents.length} 个`);
            if (businessComponents.length > 0) {
                console.log(`   示例: ${businessComponents.slice(0, 3).join(', ')}`);
            }
            
            // 检查预设模块
            const presetModules = Object.keys(eslintrcContent.globals || {}).filter(key => 
                ['$http', 'request', 'requestAll', 'cancelAll', 'preprocessor', 'fh', 'fz', 'toHex', 'dataURIToBlob', 'download', 'transParams'].includes(key)
            );
            console.log(`   预设模块: ${presetModules.length} 个`);
            if (presetModules.length > 0) {
                console.log(`   示例: ${presetModules.slice(0, 5).join(', ')}`);
            }
            
            // 检查自定义hooks
            const customHooks = Object.keys(eslintrcContent.globals || {}).filter(key => 
                key.endsWith('Hooks') || key.endsWith('hooks')
            );
            console.log(`   自定义hooks: ${customHooks.length} 个`);
            if (customHooks.length > 0) {
                console.log(`   示例: ${customHooks.join(', ')}`);
            }
        } else {
            console.log('❌ .eslintrc-auto-import.json 文件不存在');
        }
        
        console.log('🎉 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    testConfigGeneration();
}

module.exports = { testConfigGeneration };
