# 配置文件生成脚本说明

本目录包含了用于生成项目配置文件的脚本，主要解决 `.eslintrc-auto-import.json` 文件中缺少 `build-pre` 脚本生成的依赖项的问题。

## 脚本说明

### 1. `generate-auto-import-config.js` (推荐使用)

**用途**: 统一的自动导入配置生成脚本，替代 vite 命令执行时的文件生成过程

**功能**:
- 生成 `.eslintrc-auto-import.json` 文件
- 生成 `components.d.ts` 文件
- 生成 `auto-imports.d.ts` 文件
- 包含所有业务组件、预设模块、自定义hooks等

**使用方法**:
```bash
# 完整的配置生成流程
npm run build-pre && node scripts/generate-auto-import-config.js

# 或者使用 lint 命令（已配置）
npm run lint
```

**注意**: 运行此脚本前必须先执行 `npm run build-pre` 生成业务预设

### 2. `generate-eslint-config.js` (原有脚本)

**用途**: 原有的 ESLint 配置生成脚本

**使用方法**:
```bash
npm run generate-eslint
```

## 问题解决

### 问题: `pnpm lint` 后 `.eslintrc-auto-import.json` 文件缺少依赖项

**原因**:
- `generate-eslint-config.js` 脚本在 `build-pre` 脚本之前运行
- 业务预设文件 (`auto-import-business-preset.json`) 还未生成
- 模块缓存导致旧的预设被使用

**解决方案**:
使用新的 `generate-auto-import-config.js` 脚本，确保按正确顺序执行：
```bash
npm run build-pre
node scripts/generate-auto-import-config.js
```

或者直接使用 lint 命令（已配置正确的执行顺序）：
```bash
npm run lint
```

### 正常的 `.eslintrc-auto-import.json` 文件应该包含:

1. **Vue 相关全局变量**: Component, ComputedRef, Ref, VNode 等
2. **组合式 API**: computed, ref, reactive, watch, onMounted 等
3. **Vue Router**: useRoute, useRouter, onBeforeRouteLeave 等
4. **VueUse 函数**: 大量的 use* 函数
5. **Pinia**: defineStore, storeToRefs, mapActions 等
6. **业务组件自动导入**:
   - BS* 前缀: BSCustomApprovalForm, BSFileTypeTree 等
   - Bs* 前缀: BsCustomApprovalForm, BsFileTypeTree 等
   - 小写版本: bsCustomApprovalForm, bsFileTypeTree 等
   - 原始名称: CustomApprovalForm, FileTypeTree 等
7. **自定义 hooks**: UseApprovalProcessHooks, IndexHooks 等
8. **预设模块**:
   - wp-request: $http, request, requestAll, cancelAll
   - wp-preprocessor: preprocessor, fh, fz, toHex, dataURIToBlob, download, transParams
   - @/api: api

### 验证配置是否正确

手动检查文件内容:
```bash
# 检查全局变量数量
cat .eslintrc-auto-import.json | jq '.globals | length'

# 检查是否包含业务组件
cat .eslintrc-auto-import.json | jq '.globals | keys | map(select(startswith("BS")))'

# 检查是否包含预设模块
cat .eslintrc-auto-import.json | jq '.globals | keys | map(select(. == "$http" or . == "request" or . == "preprocessor"))'
```

## 当前的 package.json 脚本

```json
{
  "scripts": {
    "lint": "npm run build-pre && eslint src --ext .ts,.tsx,.vue",
    "build-pre": "tsnd -P ./src/utils/scripts/tsconfig-build.json src/utils/scripts/build-pre.ts --run-preset",
    "generate-eslint": "node scripts/generate-eslint-config.js"
  }
}
```

## 使用说明

1. **日常使用**: 直接运行 `npm run lint`，会自动执行 `build-pre` 然后进行 ESLint 检查
2. **手动生成配置**: 如果需要单独生成自动导入配置文件，运行：
   ```bash
   npm run build-pre
   node scripts/generate-auto-import-config.js
   ```

这样确保了 `build-pre` 在外部单独执行，保持了当前的脚本结构。
