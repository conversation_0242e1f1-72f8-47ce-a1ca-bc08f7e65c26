# 配置文件生成脚本说明

本目录包含了用于生成项目配置文件的脚本，主要解决 `.eslintrc-auto-import.json` 文件中缺少 `build-pre` 脚本生成的依赖项的问题。

## 脚本说明

### 1. `ensure-complete-config.js` (推荐使用)

**用途**: 智能检查并确保配置文件完整性的脚本

**功能**:
- 检查 `.eslintrc-auto-import.json` 文件是否存在且完整
- 验证是否包含业务组件、预设模块、自定义hooks等关键内容
- 如果配置不完整，自动重新生成
- 验证其他相关配置文件

**使用方法**:
```bash
# 直接运行
npm run ensure-config

# 或者在 lint 时自动运行（已配置）
npm run lint
```

**检查项目**:
- ✅ 业务组件 (BS*/Bs*/bs* 前缀)
- ✅ 预设模块 ($http, request, preprocessor, fh, fz 等)
- ✅ 自定义hooks (*Hooks/*hooks)
- ✅ Vue核心 (ref, reactive, computed, watch 等)
- ✅ 全局变量数量 (应该 >= 200)

### 2. `generate-complete-config.js`

**用途**: 完整的配置生成脚本

**功能**:
- 强制运行 `build-pre` 脚本生成业务预设
- 生成所有相关配置文件
- 提供详细的调试信息

**使用方法**:
```bash
node scripts/generate-complete-config.js
```

### 3. `generate-eslint-config.js` (原有脚本，已优化)

**用途**: 原有的 ESLint 配置生成脚本，已添加调试信息

**使用方法**:
```bash
npm run generate-eslint
```

### 4. `test-config-generation.js`

**用途**: 测试配置生成功能的脚本

**功能**:
- 检查各种模块的导入状态
- 分析当前配置文件内容
- 提供详细的诊断信息

**使用方法**:
```bash
node scripts/test-config-generation.js
```

## 问题解决

### 问题: `pnpm lint` 后 `.eslintrc-auto-import.json` 文件缺少依赖项

**原因**: 
- `generate-eslint-config.js` 脚本在 `build-pre` 脚本之前运行
- 业务预设文件 (`auto-import-business-preset.json`) 还未生成
- 模块缓存导致旧的预设被使用

**解决方案**:
1. 使用新的 `ensure-complete-config.js` 脚本（推荐）
2. 或者手动运行：
   ```bash
   npm run build-pre
   npm run generate-eslint
   ```

### 正常的 `.eslintrc-auto-import.json` 文件应该包含:

1. **Vue 相关全局变量**: Component, ComputedRef, Ref, VNode 等
2. **组合式 API**: computed, ref, reactive, watch, onMounted 等
3. **Vue Router**: useRoute, useRouter, onBeforeRouteLeave 等
4. **VueUse 函数**: 大量的 use* 函数
5. **Pinia**: defineStore, storeToRefs, mapActions 等
6. **业务组件自动导入**:
   - BS* 前缀: BSCustomApprovalForm, BSFileTypeTree 等
   - Bs* 前缀: BsCustomApprovalForm, BsFileTypeTree 等
   - 小写版本: bsCustomApprovalForm, bsFileTypeTree 等
   - 原始名称: CustomApprovalForm, FileTypeTree 等
7. **自定义 hooks**: UseApprovalProcessHooks, IndexHooks 等
8. **预设模块**:
   - wp-request: $http, request, requestAll, cancelAll
   - wp-preprocessor: preprocessor, fh, fz, toHex, dataURIToBlob, download, transParams
   - @/api: api

### 验证配置是否正确

运行以下命令检查配置文件:
```bash
npm run ensure-config
```

或者手动检查文件内容:
```bash
# 检查全局变量数量
cat .eslintrc-auto-import.json | jq '.globals | length'

# 检查是否包含业务组件
cat .eslintrc-auto-import.json | jq '.globals | keys | map(select(startswith("BS")))'

# 检查是否包含预设模块
cat .eslintrc-auto-import.json | jq '.globals | keys | map(select(. == "$http" or . == "request" or . == "preprocessor"))'
```

## 更新的 package.json 脚本

```json
{
  "scripts": {
    "lint": "node scripts/ensure-complete-config.js && eslint src --ext .ts,.tsx,.vue",
    "ensure-config": "node scripts/ensure-complete-config.js",
    "generate-eslint": "node scripts/generate-eslint-config.js",
    "build-pre": "tsnd -P ./src/utils/scripts/tsconfig-build.json src/utils/scripts/build-pre.ts --run-preset"
  }
}
```

现在 `npm run lint` 会自动确保配置文件完整性，无需手动干预。
