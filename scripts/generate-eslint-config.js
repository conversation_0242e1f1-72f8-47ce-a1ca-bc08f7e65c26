const path = require('path');

async function generateEslintConfig() {
    try {
        const AutoImport = (await import('unplugin-auto-import/vite')).default;
        const Components = (await import('unplugin-vue-components/vite')).default;
        const { NaiveUiResolver, VantResolver } = await import('unplugin-vue-components/resolvers');
        
        // 修复 AutoApi 导入
        let AutoApi;
        try {
            const autoApiModule = await import('vitejs-plugin-api-auto-import');
            AutoApi = autoApiModule.default || autoApiModule;
        } catch (e) {
            console.warn('无法加载 AutoApi 插件:', e.message);
            AutoApi = null;
        }
        
        // 尝试导入自定义解析器和预设
        let AutoImportResolvers, AutoComponentsResolvers, AutoImportBusinessPreset;
        try {
            const resolvers = await import('../src/utils/vite/auto-resolvers.ts');
            AutoImportResolvers = resolvers.AutoImportResolvers;
            AutoComponentsResolvers = resolvers.AutoComponentsResolvers;
            AutoImportBusinessPreset = resolvers.AutoImportBusinessPreset;
            console.log('✅ 自定义解析器加载成功');
        } catch (e) {
            console.warn('无法加载自定义解析器，使用默认配置');
            AutoImportResolvers = () => [];
            AutoComponentsResolvers = () => [];
            AutoImportBusinessPreset = () => ({});
        }

        // 导入预设
        let preprocessorPreset, requestPreset, remPreset;
        try {
            preprocessorPreset = (await import('wp-preprocessor/dist/preset')).default;
            requestPreset = (await import('wp-request/dist/preset')).default;
            const { remPreset: rp } = await import('vite-plugin-fz');
            remPreset = rp;
        } catch (e) {
            console.warn('无法加载预设，使用空对象');
            preprocessorPreset = {};
            requestPreset = {};
            remPreset = {};
        }

        // 检查业务预设是否正确加载
        const businessPreset = AutoImportBusinessPreset();
        console.log('业务预设内容:', Object.keys(businessPreset).length > 0 ? '已加载' : '为空');
        
        // 创建 AutoImport 插件实例 - 完全按照 vite.config.ts 配置
        const autoImportPlugin = AutoImport({
            include: [
                /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
                /\.vue$/,
                /\.vue\?vue/, // .vue
                /\.md$/ // .md
            ],
            imports: [
                preprocessorPreset,
                requestPreset,
                remPreset,
                {
                    '@/api': ['api']
                },
                businessPreset,
                // presets
                'vue',
                'vue-router',
                '@vueuse/core',
                'pinia'
            ],
            eslintrc: {
                enabled: true,
                filepath: './.eslintrc-auto-import.json'
            },
            resolvers: [AutoImportResolvers()],
            dts: true,
            dirs: []
        });

        // 创建 Components 插件实例 - 完全按照 vite.config.ts 配置
        const componentsPlugin = Components({
            resolvers: [NaiveUiResolver(), VantResolver(), AutoComponentsResolvers()]
        });

        // 创建 AutoApi 插件实例（如果可用）- 完全按照 vite.config.ts 配置
        const autoApiPlugins = [];
        if (AutoApi && typeof AutoApi === 'function') {
            autoApiPlugins.push(
                AutoApi({
                    resolveAliasName: '@/api/apis',
                    dir: 'src/api/apis'
                }),
                AutoApi({
                    name: '$alert',
                    resolveAliasName: '@/alert',
                    dir: 'src/alert'
                }),
                AutoApi({
                    name: '$utils',
                    resolveAliasName: '@/utils/utils',
                    dir: 'src/utils/utils'
                }),
                AutoApi({
                    name: '$hooks',
                    resolveAliasName: '@/hooks',
                    dir: 'src/hooks'
                }),
                AutoApi({
                    name: '$datas',
                    resolveAliasName: '@/data',
                    dir: 'src/data'
                })
            );
        }

        // 模拟更完整的 Vite 构建上下文
        const mockContext = {
            resolve: (id) => ({ id }),
            load: () => null,
            transform: () => null,
            meta: {
                rollupVersion: '3.0.0'
            }
        };

        const mockConfig = {
            command: 'build',
            mode: 'production',
            root: process.cwd(),
            plugins: [],
            build: {
                outDir: 'dist_management'
            },
            resolve: {
                alias: {
                    '@': path.resolve(process.cwd(), 'src')
                }
            },
            optimizeDeps: {
                include: [],
                exclude: []
            }
        };

        // 安全地处理 AutoImport 插件
        try {
            if (autoImportPlugin.configResolved) {
                await autoImportPlugin.configResolved.call(mockContext, mockConfig);
            }
            if (autoImportPlugin.buildStart) {
                await autoImportPlugin.buildStart.call(mockContext, {});
            }
            console.log('✅ AutoImport 配置生成成功');
        } catch (error) {
            console.warn('⚠️ AutoImport 生成失败:', error.message);
        }

        // 安全地处理 Components 插件
        try {
            if (componentsPlugin.configResolved) {
                await componentsPlugin.configResolved.call(mockContext, mockConfig);
            }
            if (componentsPlugin.buildStart) {
                await componentsPlugin.buildStart.call(mockContext, {});
            }
            console.log('✅ Components 配置生成成功');
        } catch (error) {
            console.warn('⚠️ Components 生成失败:', error.message);
        }

        // 安全地处理 AutoApi 插件
        for (const [index, autoApiPlugin] of autoApiPlugins.entries()) {
            try {
                if (autoApiPlugin.configResolved) {
                    await autoApiPlugin.configResolved.call(mockContext, mockConfig);
                }
                if (autoApiPlugin.buildStart) {
                    await autoApiPlugin.buildStart.call(mockContext, {});
                }
                console.log(`✅ AutoApi 插件 ${index + 1} 配置生成成功`);
            } catch (error) {
                console.warn(`⚠️ AutoApi 插件 ${index + 1} 生成失败:`, error.message);
            }
        }
        
        console.log('✅ 所有配置文件生成完成');
    } catch (error) {
        console.error('❌ 生成失败:', error);
    }
}

generateEslintConfig();