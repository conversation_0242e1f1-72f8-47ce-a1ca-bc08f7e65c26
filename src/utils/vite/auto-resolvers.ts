import { camelCase } from 'lodash';
export { AutoImportBusinessPreset } from '../scripts/build-pre';
const businessComponents = (name: string, isComponents: boolean) => {
    const fileName = camelCase(name.replace(/^Bs/i, '').trim())
        .replace(/([A-Z])/g, '-$1')
        .toLowerCase();
    return {
        as: name,
        ...(isComponents ? {} : { name: 'default' }),
        from: `@/components/business/${fileName}.vue`
    };
};
export const AutoImportResolvers = () => [
    (name: string) => {
        if (/^BS\w.*/i.test(name)) {
            return businessComponents(name, false);
        }
        const m = name.match(/^(use.*)Hooks$/);
        if (m) {
            return {
                as: name,
                name: m[1],
                from: '@/hooks'
            };
        }
    }
];
export const AutoComponentsResolvers = () => [
    (name: string) => {
        if (/^Bs\w.*/i.test(name)) {
            return businessComponents(name, true);
        }
    }
];
