import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    {
        path: 'approval',
        name: 'Approval',
        meta: {
            title: '审批管理'
        },
        redirect: '/approval/approval-process',
        children: [
            {
                path: 'approval-preset-management',
                name: 'approval-preset-management',
                meta: {
                    title: '流程模版管理',
                    icon: 'menus-process2',
                    permissions: [
                        { name: '新增流程', code: 'approvalProcessPresetAdd' },
                        { name: '编辑流程', code: 'approvalProcessPresetEdit' },
                        { name: '分发流程', code: 'approvalProcessPresetDispense' }
                    ]
                },
                component: () => import('@/views/approval/approval-process-preset.vue')
            },
            {
                path: 'approval-process-management',
                name: 'approval-process-management',
                meta: {
                    title: '审批流程管理',
                    icon: 'menus-process7',
                    permissions: [
                        { name: '详情', code: 'approvalProcessManagementView' },
                        { name: '编辑', code: 'approvalProcessManagementEdit' },
                        { name: '发布记录', code: 'approvalProcessManagementPublish' }
                    ]
                },
                component: () => import('@/views/approval/approval-process-management.vue')
            },
            {
                path: 'approval-process',
                name: 'ApprovalProcess',
                meta: {
                    title: '审批待办',
                    icon: 'menus-process6',
                    permissions: [
                        { name: '审批待办处理', code: 'approvalPendingProcess' },
                        { name: '审批待办查看', code: 'approvalPendingView' },
                        { name: '审批待办撤回', code: 'approvalPendingRecall' }
                    ]
                },
                component: () => import('@/views/approval/index.vue')
            },
            {
                path: 'approval-process-monitoring',
                name: 'approval-process-monitoring',
                meta: {
                    title: '流程监控',
                    icon: 'menus-process5',
                    permissions: [
                        { name: '查看', code: 'approvalProcessMonitoringView' },
                        { name: '节点管理', code: 'approvalProcessMonitoringNodeView' }
                    ]
                },
                component: () => import('@/views/approval/approval-process-monitoring.vue')
            }
        ]
    }
];
export default routes;
