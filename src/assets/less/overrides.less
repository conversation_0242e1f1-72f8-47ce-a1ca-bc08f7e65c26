:root {
    --tab-split-menu-width: 65px;
    --menu-width: 240px;
    --min-menu-width: 60px;
    --transition-time: 0.3s ease;
    --logo-height: 60px;
    --tab-height: 40px;
    --footer-height: 45px;
    --h5-bottom-nav-height: 50px;
    --primary-color: #005eff;
}

/**
 * 中一检测TTB模式下，顶部logo、nav、menu样式配置
 */
.zy-scroller-menu {
    .n-scrollbar-content {
        height: 100% !important;
        .n-menu {
            height: 100% !important;

            .n-menu-item {
                height: 100% !important;
                --n-item-text-color: #fff;
                --n-item-text-color-hover-horizontal: #fff;
                --n-item-text-color-active-horizontal: #fff;
                --n-item-text-color-active-hover-horizontal: #fff;
                .n-menu-item-content--selected {
                    border-bottom: none !important;
                    background: linear-gradient(181deg, #86ddff 1%, rgba(134, 221, 255, 0) 108%);
                }
                .n-menu-item-content__icon {
                    display: none !important;
                }
                .n-menu-item-content-header {
                    padding: 0 !important;
                    font-size: 16px !important;
                    // 不省略文本
                    text-overflow: clip !important;
                    white-space: nowrap !important;
                    overflow: visible !important;
                }
            }
        }
    }
}

// 首页路由
.home-top-menu-scrollbar {
    .n-scrollbar-content {
        height: 100% !important;
        .n-menu {
            height: 100% !important;
            .n-menu-item {
                height: 100% !important;

                .n-menu-item-content-header {
                    padding: 0 !important;
                    font-size: 16px !important;
                    // 不省略文本
                    text-overflow: clip !important;
                    white-space: nowrap !important;
                    overflow: visible !important;
                }
            }
        }
    }
}

.signature-preview-modal.n-modal {
    .n-card-header {
        .n-base-close {
            color: #fff;
            border-radius: 100%;
            font-size: 13px;
            padding: 11px;
            background: linear-gradient(272deg, #4a9bff 6%, #005eff 98%);
            &:hover {
                background: linear-gradient(0deg, #4a9bff 6%, #005eff 98%);
                color: #fff !important;
            }
        }
    }
}
