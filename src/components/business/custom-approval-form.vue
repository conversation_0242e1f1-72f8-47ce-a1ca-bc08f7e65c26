<template>
    <div class="custom-approval-form">
        <n-form class="useFormDialog" label-placement="left" :show-feedback="false" :label-width="110">
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="申请人" :span="12">
                    <n-input :value="formData.applicant || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="申请日期" :span="12">
                    <n-input :value="formatDate(formData.applyDate)" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="发放类型" :span="12">
                    <n-input
                        :value="String(getDistributeTypeLabel(formData.distributeType) ?? '')"
                        readonly
                        placeholder=""
                    />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" :span="12">
                    <n-input :value="String(getFileTypeLabel(formData.fileType) ?? '')" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" :span="12">
                    <n-input :value="String(formData.category ?? '')" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="发放原因" :span="12">
                    <n-input :value="String(formData.reasonDictNodeId ?? '')" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="其他原因" :span="24">
                    <n-input :value="String(formData.otherReason ?? '')" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="希望发放日期" :span="12">
                    <n-input :value="formatDate(formData.wishDistributeDate)" readonly placeholder="" />
                </n-form-item-gi>
            </n-grid>

            <div class="mt-16px">
                <n-form-item label="发放清单" class="data-table"> </n-form-item>
                <vxe-table :data="flatTableData" border :span-method="spanMethod" :tooltip-config="tooltipConfig">
                    <vxe-column field="fileName" title="文件名称" width="160" />
                    <vxe-column field="fileNo" title="文件编号" width="140" />
                    <vxe-column field="version" title="版本/版次" width="120" />
                    <vxe-column field="fileForm" title="文件形式" width="120" :formatter="fileFormFormatter" />
                    <vxe-column field="filePermission" title="文件权限" width="120" />
                    <vxe-column field="receiver" title="接收方" width="120" />
                    <vxe-column
                        field="receivedBy"
                        title="接收人"
                        width="180"
                        show-overflow="tooltip"
                        :formatter="receivedByFormatter"
                    />
                </vxe-table>
            </div>
        </n-form>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { computed } from 'vue';
import { VxeTable, VxeColumn } from 'vxe-table';
const props = withDefaults(
    defineProps<{
        modelValue?: any;
    }>(),
    {
        modelValue: ''
    }
);

const formData = computed(() => {
    return props.modelValue.data || {};
});
function formatDate(val: number | string) {
    if (!val) return '';
    return dayjs(Number(val)).format('YYYY-MM-DD');
}
function getDistributeTypeLabel(val: number) {
    if (val === 1) return '内部发放';
    if (val === 2) return '外部发放';
    return val;
}
function getFileTypeLabel(val: number) {
    if (val === 1) return '内部文件';
    if (val === 2) return '外部文件';
    return val;
}
interface PermissionItem {
    fileForm: string | number;
    filePermission: string;
    receiver: string;
    receivedBy: any[];
}
interface DistributeItem {
    fileName: string;
    fileNo: string;
    version: string;
    permissions: PermissionItem[];
}
// 合并单元格数据处理
const flatTableData = computed(() => {
    const val = (props.modelValue.data as any) || {};
    const list: DistributeItem[] = Array.isArray(val?.distributeList) ? val.distributeList : [];
    const arr: any[] = [];
    list.forEach((file, fileIndex) => {
        (file.permissions || []).forEach((perm, permIndex) => {
            arr.push({
                fileIndex,
                permIndex,
                fileName: file.fileName,
                fileNo: file.fileNo,
                version: file.version,
                fileForm: perm.fileForm,
                filePermission: perm.filePermission,
                receiver: perm.receiver,
                receivedBy: perm.receivedBy
            });
        });
    });
    return arr;
});
function spanMethod({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) {
    // 0,1,2列合并
    const row = flatTableData.value[rowIndex];
    const val = (props.modelValue.data as any) || {};
    const list: DistributeItem[] = Array.isArray(val?.distributeList) ? val.distributeList : [];
    const file = list[row.fileIndex];
    const permIndex = row.permIndex;
    const permCount = file?.permissions?.length || 1;
    if ([0, 1, 2].includes(columnIndex)) {
        if (permIndex === 0) {
            return { rowspan: permCount, colspan: 1 };
        } else {
            return { rowspan: 0, colspan: 0 };
        }
    }
    return { rowspan: 1, colspan: 1 };
}
function fileFormFormatter({ cellValue }: { cellValue: number | string }) {
    if (cellValue === 1 || cellValue === '1') return '电子文件';
    if (cellValue === 2 || cellValue === '2') return '纸质文件';
    return cellValue;
}
function receivedByFormatter({ cellValue }: { cellValue: any }) {
    if (Array.isArray(cellValue) && cellValue.length > 0) {
        return cellValue.map((p: any) => p.userName || p.name).join('，');
    }
    return '';
}
const tooltipConfig = {
    contentMethod({ column, row }: { column: any; row: any }) {
        if (column.field === 'receivedBy') {
            if (Array.isArray(row.receivedBy) && row.receivedBy.length > 0) {
                return row.receivedBy.map((p: any) => p.userName || p.name).join('，');
            }
            return '未选择接收人';
        }
    }
};
</script>

<style scoped lang="less">
.custom-approval-form {
    width: 100%;
}
.data-table {
    :deep(.n-form-item-blank) {
        display: none;
    }
}
</style>
