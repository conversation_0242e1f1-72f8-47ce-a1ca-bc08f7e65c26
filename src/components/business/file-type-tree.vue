<template>
    <div class="dict-tree w-220px h-full max-h-[calc(100vh-240px)] overflow-y-auto overflow-x-hidden">
        <n-tree
            ref="dictTreeRef"
            class="bg-#fff"
            v-model:checked-keys="checkedKeys"
            @update:checked-keys="handleCheckedKeys"
            v-model:expanded-keys="expandedKeys"
            @update:expanded-keys="handleExpandedKeys"
            block-line
            :data="treeData"
            checkable
            expand-on-click
            selectable
            cascade
            :render-label="renderLabel"
        />
    </div>
</template>

<script setup lang="ts">
import { NEllipsis, TreeOption } from 'naive-ui';
import { getAllKeys, isArrayChanged } from '@/utils/common';

interface Props {
    modelValue?: string[];
    width?: string;
    autoSelectAll?: boolean;
    type?: 'internal' | 'external' | 'book';
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    width: '220px',
    autoSelectAll: true,
    type: 'internal'
});

const emit = defineEmits<{
    (e: 'update:modelValue', value: string[]): void;
    (e: 'change', value: string[]): void;
}>();

// 使用 useVModels 实现双向绑定
const { modelValue } = useVModels(props, emit);

// 树形数据
const treeData = ref<any[]>([]);

// 内部选中的键值（包含"全部"选项）
const checkedKeys = ref<string[]>([]);

// 展开的键值
const expandedKeys = ref<string[]>(['0']);
const handleExpandedKeys = (keys: string[]) => {
    expandedKeys.value = keys;
};

// 获取文件类别树数据
const getDocCategoryIdsOptions = async () => {
    try {
        const dict = await api.sass.api.v1.dict.get('file_business_dictionary');
        let categoryId = '';
        switch (props.type) {
            case 'internal':
                categoryId = dict.data.data[0].extra.fileCategory.internal;
                break;
            case 'external':
                categoryId = dict.data.data[0].extra.fileCategory.external;
                break;
            case 'book':
                categoryId = dict.data.data[0].extra.fileCategory.book;
                break;
        }
        if (!categoryId) {
            window.$message.error('获取文件类别树字典数据失败');
            return;
        }
        const res = await $apis.nebula.api.v1.businessDictionary.node.tree({
            id: categoryId
        });
        treeData.value = [
            {
                key: '0',
                label: '全部',
                children: $utils.treeData.convertTreeData(res.data)
            }
        ];
    } catch (error) {
        console.error('获取文件类别树数据失败:', error);
        treeData.value = [];
    }
};

// 渲染标签
const renderLabel = (info: { option: TreeOption }) => {
    return h(
        NEllipsis,
        {
            style: { width: '100%' }
        },
        {
            default: () => info.option.label
        }
    );
};

const handleCheckedKeys = (keys: string[]) => {
    checkedKeys.value = keys;
    const filteredKeys = keys.filter((item) => item !== '0');
    if (isArrayChanged(modelValue.value, filteredKeys)) {
        modelValue.value = filteredKeys;
        emit('change', filteredKeys);
    }
};

// 获取所有可用的key（不包含"全部"）
const getAllAvailableKeys = () => {
    return getAllKeys(treeData.value).filter((item) => item !== '0');
};

// 组件挂载时初始化数据
onMounted(async () => {
    await getDocCategoryIdsOptions();
    // 如果启用自动全选且当前没有选中项，则自动全选
    if (props.autoSelectAll && (!modelValue.value || modelValue.value.length === 0)) {
        nextTick(() => {
            checkedKeys.value = getAllAvailableKeys();
            emit('change', checkedKeys.value);
        });
    }
});

defineExpose({
    treeData
});
</script>

<style scoped lang="less">
.dict-tree {
    width: v-bind(width);
}
.dict-tree::-webkit-scrollbar {
    width: 4px;
    background: #f4f4f4;
    border-radius: 4px;
}
.dict-tree::-webkit-scrollbar-thumb {
    background: #bfbfbf;
    border-radius: 4px;
    transition: background 0.3s;
}
.dict-tree::-webkit-scrollbar-thumb:hover {
    background: #b0b0b0;
}
</style>
