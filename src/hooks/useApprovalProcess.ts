import { ApproveNode, StartRequest } from '@/api/sass/api/v1/workflow/index';

// 错误统一处理
function handleError(err: any) {
    $alert.dialog({
        title: '错误',
        content: err?.message || String(err) || '未知错误'
    });
}

export async function useApprovalProcess(businessId: string, formContent: string) {
    try {
        // 获取详情
        const res = await api.sass.api.v1.workflow.templates.get({ businessId });
        const { kind, custom, department, versionId } = res.data;

        // 审批人员缺失弹窗
        const lackPeople = (data: Record<string, any>) => {
            return new Promise<void>((resolve) => {
                $alert.dialog({
                    title: '审批人员缺失',
                    width: '600px',
                    content: BsProcessLackPersonnel,
                    props: {
                        modelValue: data,
                        onClose: () => resolve()
                    }
                });
            });
        };
        // 审批单位选择弹窗
        const lackDepartment = (options: Record<string, any>) => {
            return new Promise<string>((resolve, reject) => {
                $alert.dialog({
                    title: '审批单位选择',
                    width: '600px',
                    content: BsProcessLackDepartment,
                    props: {
                        options,
                        onSave: (val: string) => resolve(val),
                        onCancel: () => reject(new Error('用户取消操作，未选择审批单位！'))
                    }
                });
            });
        };

        const customPeople = ref<Record<string, any>[]>([]);
        const departments = ref<Record<string, any>[]>([]);
        const departmentId = ref<string>('');
        const departmentPeople = ref<Record<string, any>>({});

        // 部门审批
        if (kind === 'department') {
            if (department === null) {
                handleError(new Error('审批环节暂未配置，请联系系统管理员进行配置！'));
                return Promise.reject();
            }
            if (department.nodeKind === 'approve-cc' && department.ccKind === 'custom') {
                departmentPeople.value = {
                    ccIds: []
                };
                await lackPeople({ type: 'department', data: departmentPeople.value });
            }
            try {
                departments.value = await (await api.sass.api.v1.organizationUserInfo.getDepartByUser()).data;
            } catch (err) {
                handleError(err);
                return Promise.reject();
            }
            try {
                departmentId.value = await lackDepartment(departments.value);
            } catch (err) {
                handleError(err);
                return Promise.reject();
            }
        } else {
            if (custom === null) {
                handleError(new Error('审批环节暂未配置，请联系系统管理员进行配置！'));
                return Promise.reject();
            }
            custom.forEach((item: Record<string, any>) => {
                if (item.approvalKind === 'custom') {
                    const node: ApproveNode = {
                        nodeId: item.nodeId,
                        nodeName: item.nodeName,
                        approverIds: []
                    };
                    if (item.nodeKind === 'approve-cc') {
                        node.ccIds = [];
                    }
                    customPeople.value.push(node);
                }
            });
            if (customPeople.value.length > 0) {
                try {
                    await lackPeople({ type: 'custom', data: customPeople.value });
                } catch (err) {
                    handleError(err);
                    return Promise.reject();
                }
            }
        }
        const data: StartRequest = {
            flowVersionId: versionId,
            businessId: businessId,
            formContent
        };
        if (customPeople.value?.length > 0) {
            data.nodes = customPeople.value;
        }
        if (departmentPeople.value?.ccIds?.length > 0) {
            data.ccIds = departmentPeople.value.ccIds;
        }
        if (departmentId.value) {
            data.departmentId = departmentId.value;
        }
        try {
            await api.sass.api.v1.workflow.workflow.start(data);
        } catch (err) {
            handleError(err);
            return Promise.reject();
        }
    } catch (err) {
        handleError(err);
        return Promise.reject();
    }
}

export default useApprovalProcess;
