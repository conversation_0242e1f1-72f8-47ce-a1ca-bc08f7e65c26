import { TableListParams } from '@/api/typing';

export default {
    list(params: TableListParams) {
        return request({
            url: '/nebula/api/v1/business-dictionary/list',
            method: 'get',
            params
        });
    },
    node: {
        list(params: { dictionaryId: string; parentId?: string }) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/list',
                method: 'get',
                params
            });
        },
        create(data: DictionaryNodeForm) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/create',
                method: 'post',
                data
            });
        },
        update(data: DictionaryNodeList) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/update',
                method: 'post',
                data
            });
        },
        delete(id: string) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/delete',
                method: 'post',
                data: { nodeId: id }
            });
        },
        move(data: { id: string; sort: number }) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/move',
                method: 'post',
                data
            });
        },
        tree(params: { id: string }) {
            return request({
                url: '/nebula/api/v1/business-dictionary/node/tree',
                method: 'get',
                params
            });
        }
    }
};

export interface DictionaryNodeForm {
    /**
     * 字典id
     */
    dictionaryId: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 父id
     */
    parentId?: string;
    /**
     * 备注
     */
    remark?: string;
    [property: string]: any;
}

export interface DictionaryNodeList {
    /**
     * 节点id
     */
    id: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 父id
     */
    parentId: string;
    /**
     * 备注
     */
    remark: string;
    /**
     * 排序序号
     */
    sort: number;
    /**
     * 是否启用
     */
    status: boolean;
    /**
     * 更新时间
     */
    updatedAt: number;
    /**
     * 更新人
     */
    updatedBy: string;
    [property: string]: any;
}
