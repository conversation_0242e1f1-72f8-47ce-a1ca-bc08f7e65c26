export default {
  getList: {
    method: 'get',
    url: '/api/v1/issuance-application/list'
  }
}

export interface IssuanceApplicationRow {
  id: string;
  serialNumber?: number;
  issuanceType?: string;
  fileType?: string;
  category?: string;
  applyDate?: string;
  expectedIssueDate?: string;
  reviewer?: string;
  approver?: string;
  issueCount?: number;
  signCount?: number;
  disposalCount?: number;
  status?: string;
  applicant?: string;
  reason?: string;
  otherReason?: string;
  itemList?: any[];
  paperDisposalList?: any[];
}