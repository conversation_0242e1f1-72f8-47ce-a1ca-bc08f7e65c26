// 文件状态映射及类型
const statusMap = [
    {
        label: '未知',
        value: 0,
        type: 'default' as const
    },
    {
        label: '即将作废',
        value: 1,
        type: 'error' as const
    },
    {
        label: '即将实施',
        value: 2,
        type: 'primary' as const
    },
    {
        label: '有效',
        value: 3,
        type: 'success' as const
    },
    {
        label: '拟修订',
        value: 4,
        type: 'default' as const
    }
];

// 是否有附件
const hasAttachmentOptions: any[] = [
    { label: '是', value: true },
    { label: '否', value: false }
];

// 认证方式
const certTypeOptions: any[] = [
    { label: 'ISO9001', value: 'ISO9001' },
    { label: 'CAS', value: 'CAS' },
    { label: 'CMA', value: 'CMA' }
];

// 所属领域
const domainOptions: any[] = [
    { label: '管理', value: '管理' },
    { label: '技术', value: '技术' },
    { label: '财务', value: '财务' }
];

export default {
    statusMap,
    hasAttachmentOptions,
    certTypeOptions,
    domainOptions
};
