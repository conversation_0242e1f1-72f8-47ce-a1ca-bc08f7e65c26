<template>
    <div class="approval-process">
        <n-tabs v-model:value="activeTab" class="bg-#fff px-10px" type="line" @update:value="handleTabChange">
            <n-tab v-for="item in tabs" :key="item.value" :name="item.value"> {{ item.label }} </n-tab>
        </n-tabs>
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1000,
                maxHeight: 'calc(100vh - 470px)'
            }"
            :params="params"
            :data-api="api.sass.api.v1.workflow.task.list[activeTab as TabType]"
            :search-props="{
                showAdd: false,
                searchInputPlaceholder: '请输入流程名称'
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @reset="init"
        >
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #search_form_middle>
                <n-date-picker
                    v-if="activeTab === 'todos'"
                    class="w-400px!"
                    v-model:value="params.taskCreatedTime"
                    start-placeholder="到达开始时间"
                    end-placeholder="到达结束时间"
                    type="datetimerange"
                ></n-date-picker>
                <n-date-picker
                    class="w-400px!"
                    v-model:value="params.flowCreatedTime"
                    start-placeholder="发起开始时间"
                    end-placeholder="发起结束时间"
                    type="datetimerange"
                ></n-date-picker>
                <n-select
                    v-if="activeTab === 'initiates'"
                    class="w-160px"
                    v-model:value="params.flowNodeApproverId"
                    :options="flowPeopleOptions"
                    filterable
                    placeholder="请选择环节处理人"
                ></n-select>
                <n-select
                    v-if="activeTab !== 'initiates'"
                    class="w-160px"
                    v-model:value="params.flowCreatedUserId"
                    :options="flowPeopleOptions"
                    filterable
                    placeholder="请选择发起人"
                ></n-select>
                <n-select
                    v-if="activeTab !== 'todos'"
                    class="w-120px"
                    v-model:value="params.flowStatus"
                    :options="approvalStatus"
                    placeholder="请选择状态"
                    clearable
                ></n-select>
                <n-select
                    v-if="activeTab === 'todos'"
                    class="w-220px"
                    v-model:value="params.organizationId"
                    :options="organizationOptions"
                    placeholder="请选择来源"
                    clearable
                ></n-select>
            </template>
            <template v-if="activeTab === 'initiates'" #search_handle_after>
                <n-button @click="testInitiates">流程发起【测试专用】</n-button>
            </template>
            <template #table_currentNodeApproverNicknames="{ row }">
                <n-ellipsis :line-clamp="1" class="v-top">
                    <span v-if="row.currentNodeApproverNicknames">{{ row.currentNodeApproverNicknames }}</span>
                    <span v-else>--</span>
                </n-ellipsis>
            </template>
            <template #table_taskCreatedTime="{ row }">
                <n-time :time="row.createdAt" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_taskCompletedTime="{ row }">
                <n-time :time="row.taskCompletedTime" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_ccTime="{ row }">
                <n-time :time="row.ccTime" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_flowCreatedTime="{ row }">
                <n-time :time="row.flowCreatedTime" format="yyyy-MM-dd HH:mm:ss" />
            </template>
            <template #table_flowStatus="{ row }">
                <n-tag :type="getStatus(row.flowStatus).type" size="small" round :bordered="false">
                    {{ getStatus(row.flowStatus).label }}
                </n-tag>
            </template>
            <template #table_consultStatus="{ row }">
                <n-tag :type="row.consultStatus ? 'success' : 'default'" size="small" round :bordered="false">
                    {{ row.consultStatus ? '已阅' : '未阅' }}
                </n-tag>
            </template>

            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission v-if="activeTab === 'todos'" has="approvalPendingProcess">
                        <n-button @click="handle(row, 'handle', activeTab)" size="tiny" type="primary"> 处理 </n-button>
                    </n-permission>
                    <n-permission v-if="activeTab !== 'todos'" has="approvalPendingView">
                        <n-button @click="handle(row, 'view', activeTab, row?.consultStatus)" size="tiny">
                            查看
                        </n-button>
                    </n-permission>
                    <n-permission
                        v-if="activeTab === 'initiates' && row.flowStatus === $datas.approvalStatus.underReview.value"
                        has="approvalPendingRecall"
                    >
                        <n-button @click="reject(row)" size="tiny" type="primary"> 撤回 </n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>
<script lang="ts" setup>
import { NSelect, NButton, NSpace } from 'naive-ui';
import useStore from '@/store/modules/main';
import { WorkflowTaskList } from '@/api/sass/api/v1/workflow/task';
import { TableColumn } from 'naive-ui/es/data-table/src/interface';

const store = useStore();

type TabType = 'todos' | 'initiates' | 'done' | 'cs';
const activeTab = ref<TabType>('todos');
const tabs = ref([
    {
        label: '我的待办',
        value: 'todos'
    },
    {
        label: '我发起的',
        value: 'initiates'
    },
    {
        label: '我处理的',
        value: 'done'
    },
    {
        label: '抄送我的',
        value: 'cs'
    }
]);
const handleTabChange = (value: string) => {
    activeTab.value = value as TabType;
    init();
};

const params = ref<any>({});

// 所有列定义
const allColumns: Record<string, TableColumn> = {
    index: {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 45,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    flowName: {
        title: '流程名称',
        key: 'flowName',
        align: 'center',
        fixed: 'left',
        minWidth: 220,
        ellipsis: { tooltip: true }
    },
    currentNode: {
        title: '当前环节',
        key: 'currentNodeName',
        align: 'center',
        ellipsis: { tooltip: true }
    },
    nodeApprover: {
        title: '环节处理人',
        key: 'currentNodeApproverNicknames',
        align: 'center',
        ellipsis: { tooltip: true }
    },
    taskCreatedTime: {
        title: '到达时间',
        key: 'taskCreatedTime',
        align: 'center',
        width: 180
    },
    taskCompletedTime: {
        title: '处理时间',
        key: 'taskCompletedTime',
        align: 'center',
        width: 180
    },
    ccTime: {
        title: '抄送时间',
        key: 'ccTime',
        align: 'center',
        width: 180
    },
    flowCreatedUser: {
        title: '发起人员',
        key: 'flowCreatedUserNickname',
        align: 'center',
        ellipsis: { tooltip: true }
    },
    organizationName: {
        title: '来源',
        key: 'organizationName',
        align: 'center',
        minWidth: 80
    },
    flowCreatedTime: {
        title: '发起时间',
        key: 'flowCreatedTime',
        align: 'center',
        width: 180
    },
    flowStatus: {
        title: '流程状态',
        key: 'flowStatus',
        align: 'center',
        minWidth: 80
    },
    consultStatus: {
        title: '查阅状态',
        key: 'consultStatus',
        align: 'center',
        minWidth: 80
    },
    todo: {
        title: '操作',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 120
    }
};
// 动态列表
const columns = computed(() => {
    const {
        index,
        flowName,
        currentNode,
        nodeApprover,
        taskCreatedTime,
        taskCompletedTime,
        ccTime,
        flowCreatedUser,
        organizationName,
        flowCreatedTime,
        flowStatus,
        consultStatus,
        todo
    } = allColumns;
    switch (activeTab.value) {
        case 'todos':
            return [
                index,
                flowName,
                currentNode,
                taskCreatedTime,
                flowCreatedUser,
                organizationName,
                flowCreatedTime,
                todo
            ];
        case 'initiates':
            return [index, flowName, currentNode, nodeApprover, flowCreatedTime, flowStatus, todo];
        case 'done':
            return [index, flowName, taskCompletedTime, flowCreatedUser, flowCreatedTime, flowStatus, todo];
        case 'cs':
            return [index, flowName, ccTime, flowCreatedUser, flowCreatedTime, flowStatus, consultStatus, todo];
        default:
            return [index, flowName];
    }
});

// 环节处理人 - 发起人
const flowPeopleOptions = ref([]);
const getFlowPeopleOptions = async () => {
    await api.sass.api.v1.organizationUserInfo.groupAllList().then((res) => {
        flowPeopleOptions.value = res.data?.map((item: any) => {
            return {
                label: item.nickname,
                value: item.id
            };
        });
    });
};

const approvalStatus = computed(() => {
    return Object.values($datas.approvalStatus)
        .filter((item) => item.value !== 'notStarted' && item.value !== 'cancelled')
        .map((item) => {
            return {
                label: item.label,
                value: item.value
            };
        });
});

const organizationOptions = ref([]);
const getOrganizationOptions = async () => {
    await api.sass.api.v1.organization
        .typeList({
            nodeTypes: [0, 1]
        })
        .then((res) => {
            organizationOptions.value = res.data?.map((item: any) => {
                return {
                    label: item.name,
                    value: item.id
                };
            });
        });
};

// 获取状态配置
const getStatus = (value: keyof typeof $datas.approvalStatus) => {
    return (
        $datas.approvalStatus[value] || {
            value: 'unknown',
            label: '未知状态',
            type: 'default'
        }
    );
};

// 处理
const handle = (row: WorkflowTaskList, type: 'handle' | 'view', tab: TabType, isRead = true) => {
    $alert.dialog({
        title: type === 'handle' ? '处理' : '详情',
        style: 'width: 80%; max-width: 1200px;',
        content: import('./model/pending-details.vue'),
        props: {
            flowId: row.flowId,
            taskId: row.taskId,
            ccId: row.ccId,
            type,
            permission: store.permissions,
            isRead,
            onSave: () => init(),
            onClose: () => {
                if (tab === 'cs' && !isRead) {
                    init();
                }
            }
        }
    });
};

// 撤回
const reject = (row: WorkflowTaskList) => {
    window.$dialog.warning({
        title: '提示',
        content: '确定要撤回该流程吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await api.sass.api.v1.workflow.workflow.reject(row.flowId as string);
            window.$message.success('撤回成功');
            init();
        }
    });
};

const init = async () => {
    params.value = {
        taskCreatedTime: null,
        flowCreatedTime: null,
        flowNodeApproverId: null,
        flowCreatedUserId: null,
        flowStatus: null
    };
    nextTick(() => searchTablePageRef.value?.initData());
};

// 流程发起测试逻辑（仅保留第一步：获取详情）
const testInitiates = async () => {
    const options = await (
        await api.sass.api.v1.workflow.templates.list({ noPage: true })
    ).data.data.map((item: any) => {
        return {
            label: `${item.businessId}（${item.name}）`,
            value: item.businessId
        };
    });
    $alert.dialog({
        title: '测试专用-审批流程发起',
        width: '600px',
        content: h(NSelect, {
            options,
            placeholder: '请选择一个审批流程',
            style: 'width: 100%',
            onUpdateValue: async (value: string) => {
                const formContent = JSON.stringify({
                    businessId: 'TEST_APPROVAL',
                    version: '1.0.0',
                    data: {
                        nickname: store.userInfo.nickname,
                        reason: '测试审批流程'
                    }
                });
                $hooks.useApprovalProcess(value, formContent);
            }
        })
    });
};

onMounted(() => {
    getFlowPeopleOptions();
    getOrganizationOptions();
});

const searchTablePageRef = ref();
</script>
