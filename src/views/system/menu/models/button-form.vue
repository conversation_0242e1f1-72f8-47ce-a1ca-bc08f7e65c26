<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            class="flex-v gap-y-10px"
            :model="form"
            :rules="rules"
            :label-width="90"
            :show-feedback="false"
            label-placement="left"
            require-mark-placement="left"
        >
            <n-form-item label="按钮名称" path="name">
                <n-input v-model:value="form.name" placeholder="请输入按钮名称" clearable />
            </n-form-item>
            <n-form-item label="按钮CODE" path="code">
                <n-input v-model:value="form.code" placeholder="请输入按钮CODE" clearable />
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script lang="ts" setup>
import { ButtonListData } from '@/api/sass/api/v1/button';

const props = defineProps<{
    row?: any;
    id: string;
}>();

// 按钮表单
const formRef = ref();
const form = ref<ButtonListData>({} as ButtonListData);
const rules = {
    name: { required: true, message: '请输入按钮名称', trigger: ['input', 'blur'] },
    code: { required: true, message: '请输入按钮编码', trigger: ['input', 'blur'] }
};

const submit = async () => {
    await formRef.value
        .validate()
        .then(async () => {
            const res = form.value.id
                ? await window.api.sass.api.v1.button.update(form.value as ButtonListData)
                : await window.api.sass.api.v1.button.create(form.value as ButtonListData);
            window.$message.success(res.msg as string);
        })
        .catch((error: any) => {
            window.$message.error(error[0][0].message);
            return Promise.reject(error);
        });
};

onMounted(() => {
    form.value = props.row ? { ...props.row } : {};
    form.value.menuId = props.id;
});
</script>

<style scoped></style>
