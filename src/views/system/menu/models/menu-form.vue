<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            :model="form"
            :rules="rules"
            :label-width="90"
            :show-feedback="false"
            label-placement="left"
            require-mark-placement="left"
        >
            <n-grid :cols="8" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="菜单标题" path="title" span="8">
                    <n-input v-model:value="form.title" placeholder="请输入菜单标题" clearable />
                </n-form-item-gi>
                <n-form-item-gi label="菜单名称" path="name" span="8">
                    <n-input v-model:value="form.name" placeholder="请输入菜单名称" clearable />
                </n-form-item-gi>
                <n-form-item-gi label="路径 / Url" path="url" span="8">
                    <n-input v-model:value="form.url" placeholder="请输入路径 / Url" clearable />
                </n-form-item-gi>
                <n-form-item-gi label="组件" span="8">
                    <n-select v-model:value="form.component" :options="componentsOptions" filterable />
                </n-form-item-gi>
                <n-form-item-gi label="重定向" span="8">
                    <n-input v-model:value="form.redirect" placeholder="请输入重定向路径" clearable />
                </n-form-item-gi>
                <n-form-item-gi label="排序号" span="8">
                    <n-input-number v-model:value="form.sort" :precision="0" />
                </n-form-item-gi>
                <n-form-item-gi label="父级" span="8" v-if="parentId">
                    <n-cascader
                        v-model:value="parentId"
                        :options="menus"
                        value-field="id"
                        label-field="title"
                        disabled
                    />
                </n-form-item-gi>
                <n-form-item-gi label="是否隐藏" span="3">
                    <n-switch v-model:value="form.hidden" />
                </n-form-item-gi>
                <n-form-item-gi label="是否在页签中隐藏" label-width="150px" span="3">
                    <n-switch v-model:value="form.hiddenInTab" />
                </n-form-item-gi>
                <n-form-item-gi label="是否启用" span="3">
                    <n-switch v-model:value="form.isActive" />
                </n-form-item-gi>
                <n-form-item-gi label="是否在页签中固定" label-width="150px" span="3">
                    <n-switch v-model:value="form.fixed" />
                </n-form-item-gi>
                <n-form-item-gi label="是否全屏" span="3">
                    <n-switch v-model:value="form.isFullPage" />
                </n-form-item-gi>
                <n-form-item-gi label="图标" span="8">
                    <icon-select v-model:value="form.icon" />
                </n-form-item-gi>
                <n-form-item-gi label="备注" span="8">
                    <n-input v-model:value="form.remark" type="textarea" placeholder="请输入备注" />
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script lang="ts" setup>
import { views } from '@/config/config-hooks';
import { CascaderOption } from 'naive-ui';
import { MenuListData } from '@/api/sass/api/v1/menu';

const props = defineProps<{
    menus: CascaderOption[];
    row?: MenuListData;
    parent?: MenuListData;
}>();
const formRef = ref();

const parentId = ref();

const rules = {
    title: { required: true, message: '请输入菜单标题', trigger: ['input', 'blur'] },
    name: { required: true, message: '请输入菜单名称', trigger: ['input', 'blur'] },
    url: { required: true, message: '请输入路径 / Url', trigger: ['input', 'blur'] }
};

// vue 文件列表
const componentsOptions = computed(() => {
    return Object.keys(views).map((item) => ({
        label: item.replace(/^\.\.\/views/, ''),
        value: item.replace(/^\.\.\/views/, '')
    }));
});

// 菜单表单
const form = ref<MenuListData>({} as MenuListData);

const submit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            const res = form.value.id
                ? await window.api.sass.api.v1.menu.update(form.value as MenuListData)
                : await window.api.sass.api.v1.menu.create(form.value as MenuListData);
            window.$message.success(res.msg as string);
        })
        .catch((error: any) => {
            window.$message.error(error[0][0].message);
            return Promise.reject(error);
        });
};

onMounted(async () => {
    if (props.row) {
        form.value = { ...props.row };
        parentId.value = props.row.parentId;
    } else {
        if (props.parent) {
            parentId.value = props.parent.id;
        }
        form.value = {
            isActive: true,
            parentId: parentId.value
        } as MenuListData;
    }
});
</script>

<style scoped lang="less"></style>
