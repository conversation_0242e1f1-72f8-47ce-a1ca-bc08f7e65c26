<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            class="flex-v gap-y-10px"
            :model="form"
            :rules="rules"
            :label-width="80"
            :show-feedback="false"
            label-placement="left"
            require-mark-placement="left"
        >
            <n-form-item label="岗位名称" path="name">
                <n-input v-model:value="form.name" placeholder="请输入" clearable maxlength="50" />
            </n-form-item>
            <n-form-item label="岗位编码" path="code">
                <n-input v-model:value="form.code" show-count maxlength="50" placeholder="请输入" clearable />
            </n-form-item>
            <n-form-item label="状态" required>
                <n-switch v-model:value="form.status" />
            </n-form-item>
            <n-form-item label="描述">
                <n-input maxlength="50" show-count v-model:value="form.remark" placeholder="请输入" type="textarea" />
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script lang="ts" setup>
import { PositionListData } from '@/api/sass/api/v1/position';

const props = withDefaults(
    defineProps<{
        row: PositionListData | null;
    }>(),
    {
        row: null
    }
);

const open = (row: PositionListData | null) => {
    form.value = row
        ? { ...row }
        : ({
              status: true
          } as PositionListData);
};

// 接口组表单
const formRef = ref();
const form = ref<PositionListData>({} as PositionListData);
const rules = ref({
    name: [{ required: true, message: '请输入岗位名称', trigger: ['blur', 'input'] }],
    code: [{ required: true, message: '请输入岗位编码', trigger: ['blur', 'input'] }]
});

const submit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            const res = form.value.id
                ? await window.api.sass.api.v1.position.update(form.value)
                : await window.api.sass.api.v1.position.create(form.value);
            window.$message.success(res.msg as string);
        })
        .catch((error: any) => {
            window.$message.error(error[0][0].message);
            return Promise.reject();
        });
};

onMounted(() => {
    open(props.row);
});
</script>

<style scoped></style>
