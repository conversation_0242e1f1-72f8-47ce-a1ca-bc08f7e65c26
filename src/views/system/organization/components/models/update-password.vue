<template>
    <n-modal v-model:show="show" preset="card" title="修改密码" :bordered="false" :style="{ width: '40%' }" segmented>
        <n-form
            ref="formRef"
            :rules="rules"
            :model="form"
            label-placement="left"
            require-mark-placement="left"
            label-width="130px"
        >
            <n-form-item label="操作员登录密码" required require-mark-placement="left" path="operatorPW">
                <n-input
                    v-model:value="form.operatorPW"
                    type="password"
                    placeholder="请输入当前账号的登录密码"
                ></n-input>
            </n-form-item>
            <n-form-item label="新密码" required require-mark-placement="left" path="newPass">
                <n-input v-model:value="form.newPass" type="password" placeholder="请输入新密码"></n-input>
            </n-form-item>
        </n-form>
        <template #footer>
            <n-space justify="center">
                <n-button @click="show = false">取消</n-button>
                <n-button @click="submit" type="primary">保存</n-button>
            </n-space>
        </template>
    </n-modal>
</template>

<script lang="ts" setup>
import { UserUpdatePassword } from '@/api/sass/api/v1/user';
import { FormItemRule, useMessage } from 'naive-ui';
import { utils } from 'wp-utils';

const message = useMessage();

const props = defineProps<{
    ids: string[];
}>();

const emits = defineEmits(['submit']);

const show = ref(false);
const form = ref<Record<string, any>>({});
const rules = ref<any>({
    operatorPW: {
        required: true,
        message: '请输入当前账号的登录密码',
        trigger: ['blur', 'input']
    },
    newPass: {
        required: true,
        trigger: ['input', 'blur'],
        validator(rule: FormItemRule, value: string) {
            if (!value) {
                return new Error('请输入新密码');
            } else if (!utils.checkPassword(value)) {
                return new Error('请输入8-20位,且同时包含大小写字母、数字以及特殊字符的密码！');
            }
            return true;
        }
    }
});

const open = () => {
    show.value = true;
};

const formRef = ref();
const submit = async () => {
    formRef.value?.validate(async (errors: any) => {
        if (!errors) {
            const res = await window.api.sass.api.v1.user.update_users_password({
                ...form.value,
                userIds: props.ids
            } as UserUpdatePassword);
            await message.success(res.msg as string);
            show.value = false;
            emits('submit');
        }
    });
};

defineExpose({ open });
</script>

<style scoped></style>
