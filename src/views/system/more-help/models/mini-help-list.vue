<template>
    <div class="min-h-300px mini-hellp-list">
        <n-data-table :show="false" max-height="450" :columns="columnData" :data="tableData" :row-props="rowProps">
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
// import { utils } from 'wp-utils';
import type { RowVO } from '@/api/sass/api/v1/dict';

const tableData = ref<RowVO[]>([]);

const columnData = [
    {
        title: '序号',
        key: 'serialNumber',
        width: '60px',
        render: (row: RowVO, index: number) => index + 1
    },
    {
        title: '文件名称',
        key: 'fileName',
        ellipsis: { tooltip: true },
        className: 'color-blue'
    }
    // {
    //     title: '创建时间',
    //     key: 'createdAt',
    //     render: (row: RowVO) => formatDate(row.createdAt),
    //     ellipsis: { tooltip: true }
    // },
    // {
    //     title: '创建人',
    //     key: 'createdBy',
    //     ellipsis: { tooltip: true }
    // },
    // {
    //     title: '编辑时间',
    //     key: 'updatedAt',
    //     render: (row: RowVO) => formatDate(row.updatedAt),
    //     ellipsis: { tooltip: true }
    // },
    // {
    //     title: '操作人',
    //     key: 'updatedBy',
    //     ellipsis: { tooltip: true }
    // }
];

const rowProps = (row: RowVO) => ({
    onClick: () => {
        handleSelect(row);
    }
});

const handleSelect = (row: RowVO) => {
    if (!row) return;
    $alert.dialog({
        title: `文件预览: ${row.fileName}`,
        width: '80%',
        content: import('@/components/file-preview.vue'),
        props: {
            id: row.fileId,
            name: row.fileName,
            format: row.fileType
        }
    });
};

// function formatDate(val: string | number) {
//     return val ? utils.dateFormat(val, 'YYYY-MM-DD HH:mm:ss') : '';
// }

const getFile = async () => {
    const res = await api.sass.api.v1.dict.helpList();
    tableData.value = res.data.data;
};

onMounted(() => {
    getFile();
});
</script>

<style scoped lang="less">
:deep(tr td.color-blue) {
    color: #005eff;
}
</style>
