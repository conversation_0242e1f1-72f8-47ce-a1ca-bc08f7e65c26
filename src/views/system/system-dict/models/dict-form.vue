<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            class="flex-v gap-y-10px"
            :model="data"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
        >
            <n-form-item label="字典名称" path="dictName">
                <n-input v-model:value="data.dictName" />
            </n-form-item>
            <n-form-item label="字典类型" path="dictType">
                <n-input v-model:value="data.dictType" />
            </n-form-item>
            <n-form-item label="字段代码" path="dictCode">
                <n-input v-model:value="data.dictCode" />
            </n-form-item>
            <n-form-item label="额外字段" path="extra">
                <json-editor v-model:value="data.extra" @update:value="onJsonChange" @error="onError"></json-editor>
            </n-form-item>
            <n-form-item label="描述" path="description">
                <n-input v-model:value="data.description" type="textarea" />
            </n-form-item>
            <n-form-item label="是否启用" path="isOpen">
                <n-switch v-model:value="data.isOpen" />
            </n-form-item>
            <n-form-item label="排序" path="dictOrder">
                <n-input-number v-model:value="data.dictOrder" min="1" />
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { DictRequest } from '@/api/sass/api/v1/dict';
import { FormRules } from 'naive-ui';

const props = defineProps({
    row: {
        type: Object as PropType<DictRequest>,
        default: null
    }
});

const data = ref<DictRequest>({
    dictName: '',
    dictType: '',
    dictCode: '',
    description: '',
    isOpen: true,
    dictOrder: 1,
    extra: {}
});
const rules = ref<FormRules>({
    dictName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
    dictType: [{ required: true, message: '请选择字典类型', trigger: 'blur' }],
    dictCode: [{ required: true, message: '请输入字段代码', trigger: 'blur' }],
    description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
    isOpen: [{ required: true, message: '请选择是否启用', trigger: 'blur', type: 'boolean' }],
    dictOrder: [{ required: true, message: '请输入排序', trigger: 'blur', type: 'number' }]
});

const formRef = ref();
const hasJsonFlag = ref(true);
const submit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            if (!hasJsonFlag.value) {
                window.$message.error('请检查json格式');
                return Promise.reject();
            }
            if (props.row) {
                await api.sass.api.v1.dict.update(data.value);
            } else {
                await api.sass.api.v1.dict.create(data.value);
            }
            window.$message.success('操作成功');
        })
        .catch((err: any) => {
            window.$message.error(err[0][0].message);
            return Promise.reject();
        });
};

const onJsonChange = (json: any) => {
    data.value.extra = json;
    hasJsonFlag.value = true;
};

const onError = () => {
    hasJsonFlag.value = false;
};

onMounted(() => {
    if (props.row) {
        data.value = { ...props.row };
    }
});
</script>
<style scoped lang="less"></style>
