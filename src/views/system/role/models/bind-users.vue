<template>
    <alert-content :on-default-save="submit">
        <n-transfer v-model:value="selected" virtual-scroll :options="users" source-filterable />
    </alert-content>
</template>

<script lang="ts" setup>
import { UserListData } from '@/api/sass/api/v1/user';

const props = withDefaults(
    defineProps<{
        id: string;
    }>(),
    {
        id: ''
    }
);

const selected = ref<string[]>([]);
const rId = ref('');
const users = ref<UserListData[]>([]);

const open = (id: string) => {
    users.value = [];
    selected.value = [];
    rId.value = id;
    init();
};

const init = async () => {
    const res = await window.api.sass.api.v1.user.list({ pageSize: 10000 });
    users.value = res.data.data.map((v: any) => ({
        ...v,
        label: v.username,
        value: v.id,
        disabled: !v.status
    }));
    selected.value = (await window.api.sass.api.v1.role.users.get(rId.value)).data.data.map((v: any) => v.id);
};

const submit = async () => {
    const res = await window.api.sass.api.v1.role.users.update_users(rId.value, selected.value);
    window.$message.success(res.msg as string);
};

onMounted(() => {
    open(props.id);
});
</script>

<style scoped></style>
