<template>
    <alert-content :on-default-save="onSubmit">
        <n-form
            class="mt-2"
            ref="formRef"
            label-align="right"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            label-width="110px"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="12" :x-gap="16">
                <n-grid-item :span="12">
                    <n-form-item label="申请人">
                        <n-input v-model:value="formData.applicant" readonly disabled />
                    </n-form-item>
                </n-grid-item>
                <n-grid-item :span="12">
                    <n-form-item label="申请日期">
                        <n-input v-model:value="formData.applyDate" readonly disabled />
                    </n-form-item>
                </n-grid-item>
                <n-grid-item :span="24">
                    <n-form-item
                        label="纳入清单"
                        path="itemList"
                        required
                        label-placement="top"
                        class="include-list-item"
                    >
                        <BatchInclusionListVxeTable v-model="formData.itemList" />
                    </n-form-item>
                </n-grid-item>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import BatchInclusionListVxeTable from './batch-inclusion-list-vxe-table.vue';

const store = useStore();
const formRef = ref<FormInst>();
const props = defineProps<{ checkedRow?: any[] }>();

const formData = reactive({
    applicant: store.userInfo?.nickname || '自动生成',
    applyDate: dayjs().format('YYYY-MM-DD'),
    itemList: props.checkedRow && props.checkedRow.length > 0 ? props.checkedRow.map((row) => ({ ...row })) : []
});

const rules = computed<FormRules>(() => ({
    itemList: [
        {
            required: true,
            trigger: 'blur',
            validator: () => {
                if (!formData.itemList || !Array.isArray(formData.itemList) || formData.itemList.length === 0) {
                    return new Error('请填写纳入清单');
                }
                for (let i = 0; i < formData.itemList.length; i++) {
                    const item = formData.itemList[i];
                    if (!item.fileName) {
                        return new Error(`第${i + 1}行集团文件名称不能为空`);
                    }
                    if (!item.fileNo) {
                        return new Error(`第${i + 1}行集团文件编号不能为空`);
                    }
                }
                return true;
            }
        }
    ]
}));

const onSubmit = async () => {
    const formContent = JSON.stringify({
        businessId: 'IMPORT_GROUP_EXTERNAL_DOCS_TO_COMPANY_APPROVAL',
        version: '1.0.0',
        data: {
            data: formData.itemList,
            nickname: store.userInfo.nickname,
            reason: '纳入公司流程'
        }
    });
    await formRef.value
        ?.validate()
        .then(async () => {
            await new Promise((resolve) => {
                window.$dialog.warning({
                    title: '确认提示',
                    content: `确认后将发起审批流程，是否确认？`,
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: async () => {

                        const res = await $apis.nebula.api.v1.external.plagiarismCheck({
                            ids: formData.itemList.map((item: any) => item.id)
                        });
                        if (res.code === 0) {
                            await $hooks.useApprovalProcess(
                                'FILE_INCORPORATE',
                                formContent
                            );
                            window.$message.success('提交成功，审批通过后将纳入子公司库');
                            resolve(true);
                        } else {
                            window.$message.error(res.message || '提交失败');
                            resolve(false);
                        }
                    }
                });
            });
        })
        .catch((err: any) => {
            if (err && err[0] && err[0][0]) {
                window.$message.error(err[0][0].message);
            }
            return Promise.reject();
        });
};
</script>

<style scoped lang="less">
:deep(.n-upload-file-list) {
    margin-top: 0;
}
:deep(.include-list-item .n-form-item-label) {
    margin: 10px 0px 10px 30px !important;
    text-align: left !important;
    justify-content: flex-start !important;
}
</style>
