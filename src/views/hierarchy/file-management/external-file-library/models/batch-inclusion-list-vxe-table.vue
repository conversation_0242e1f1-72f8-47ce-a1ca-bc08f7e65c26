<template>
  <div class="batch-inclusion-list-vxe-table">
    <vxe-table
      ref="tableRef"
      :data="itemList"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
      class="batch-inclusion-vxe-table"
      keep-source
    >
      <vxe-column field="fileNo" title="集团文件编号" width="200" :edit-render="{}" show-overflow>
        <template #edit="{ row, rowIndex }">
          <n-select
            v-model:value="row.fileNo"
            :options="fileNoOptions"
            filterable
            clearable
            remote
            @search="debouncedSearchFileNo"
            @update:value="val => onFileNoSelect(rowIndex, val)"
            placeholder="选择集团文件编号"
            style="width: 180px"
          />
        </template>
      </vxe-column>
      <vxe-column field="fileName" title="集团文件名称" width="200" :edit-render="{}" show-overflow>
        <template #edit="{ row, rowIndex }">
          <n-select
            v-model:value="row.fileName"
            :options="fileNameOptions"
            filterable
            clearable
            remote
            @search="debouncedSearchFileName"
            @update:value="val => onFileNameSelect(rowIndex, val)"
            placeholder="选择集团文件名称"
            style="width: 180px"
          />
        </template>
      </vxe-column>
      <vxe-column field="originalNumber" title="原文件编号" width="180" :edit-render="{}" show-overflow>
        <template #edit="{ row }">
          <n-input v-model:value="row.originalNumber" placeholder="请输入原文件编号" />
        </template>
      </vxe-column>
      <vxe-column field="originalVersion" title="原版本/版次" width="180" :edit-render="{}" show-overflow>
        <template #edit="{ row }">
          <n-input v-model:value="row.originalVersion" placeholder="请输入原版本/版次" />
        </template>
      </vxe-column>
      <vxe-column field="actions" title="操作" width="120">
        <template #default="{ rowIndex }">
          <n-button size="tiny" type="error" @click="removeRow(rowIndex)" v-if="itemList.length > 1">删除</n-button>
        </template>
      </vxe-column>
    </vxe-table>
    <div class="table-footer">
      <n-button status="primary" size="tiny" @click="addRow">增加一行</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import 'vxe-table/lib/style.css';
import { VxeTable, VxeColumn } from 'vxe-table';
import { ref, watch, defineProps, defineEmits } from 'vue';
import debounce from 'lodash/debounce';

const props = defineProps<{ modelValue: any[] }>();
const emit = defineEmits(['update:modelValue']);

function getEmptyRow() {
  return {
    key: Date.now() + Math.random(),
    id: null,
    fileName: null,
    fileNo: null,
    originalNumber: '',
    originalVersion: ''
  };
}

const itemList = ref(props.modelValue && props.modelValue.length > 0 ? props.modelValue : []);

watch(
  () => props.modelValue,
  (val) => {
    itemList.value = val && val.length > 0 ? val : [];
  }
);

watch(
  itemList,
  (val) => {
    emit('update:modelValue', val);
  },
  { deep: true }
);

const fileNameOptions = ref<any[]>([]);
const fileNoOptions = ref<any[]>([]);

const searchFileName = async (query: string) => {
  if (!query) {
    fileNameOptions.value = [];
    return;
  }
  const res = await $apis.nebula.api.v1.external.getList({
    page: 1,
    pageSize: 20,
    noPage: true,
    name: query,
    status: 0,
    orgType: 1
  });
  const list = res?.data?.data || [];
  fileNameOptions.value = list.map((item: any) => ({
    key: item.id,
    label: item.name,
    value: item.name,
    fileNo: item.number
  }));
};

const searchFileNo = async (query: string) => {
  if (!query) {
    fileNoOptions.value = [];
    return;
  }
  const res = await $apis.nebula.api.v1.external.getList({
    page: 1,
    pageSize: 1000,
    noPage: true,
    number: query,
    status: 0,
    orgType: 1
  });
  const list = res?.data?.data || [];
  fileNoOptions.value = list.map((item: any) => ({
    key: item.id,
    label: item.number,
    value: item.number,
    fileName: item.name
  }));
};

const debouncedSearchFileName = debounce(searchFileName, 500);
const debouncedSearchFileNo = debounce(searchFileNo, 500);

function addRow() {
  itemList.value.push(getEmptyRow());
}
function removeRow(index: number) {
  itemList.value.splice(index, 1);
}

function onFileNameSelect(fileIndex: number, selectedValue: string) {
  const selected = fileNameOptions.value.find((item) => item.value === selectedValue);
  if (selected) {
    itemList.value[fileIndex].fileNo = selected.fileNo;
    itemList.value[fileIndex].id = selected.key;
  }
}
function onFileNoSelect(fileIndex: number, selectedValue: string) {
  const selected = fileNoOptions.value.find((item) => item.value === selectedValue);
  if (selected) {
    itemList.value[fileIndex].fileName = selected.fileName;
    itemList.value[fileIndex].id = selected.key;
  }
}
</script>

<style scoped lang="less">
.batch-inclusion-list-vxe-table {
  background: #fff;
  border-radius: 8px;
  padding: 16px 12px 0 12px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  width: 100%;
}
.batch-inclusion-vxe-table {
  th {
    background: #e6f7ff;
    font-weight: bold;
    color: #222;
    font-size: 14px;
    height: 40px;
    border-bottom: 1.5px solid #b6e0fe;
  }
  td {
    height: 38px;
    padding: 4px 8px;
    font-size: 13px;
    vertical-align: middle;
  }
}
.table-footer {
  margin-top: 12px;
  text-align: left;
}
</style>
