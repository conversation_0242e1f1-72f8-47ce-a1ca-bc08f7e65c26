<template>
    <div class="approval-process">
        <div v-for="(section, idx) in sections" :key="idx" class="approval-section">
            <div class="approval-title">
                <span>{{ section.title }}</span>
                <n-tag
                    round
                    :bordered="false"
                    size="small"
                    :type="section.action === 'grant' ? 'primary' : 'warning'"
                    class="ml-16px font-normal"
                    >{{ section.action === 'grant' ? '发放' : '回收' }}</n-tag
                >
            </div>
            <div class="approval-content">
                <div class="approval-row" v-for="(item, i) in section.approvers" :key="i">
                    <div class="label">{{ item.label }}</div>
                    <div class="value">{{ item.name }}</div>
                    <div class="label">{{ item.dateLabel }}</div>
                    <div class="value">{{ item.date }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        id: string;
        type: number;
    }>(),
    {
        id: '',
        type: 1
    }
);
const emit = defineEmits(['update:id', 'update:type']);
const { id, type } = useVModels(props, emit);

const sections = computed(() => {
    if (type.value === 1) {
        return [
            {
                title: '内发：电子文件-查询',
                action: 'grant',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            },
            {
                title: '内发：电子文件-查询',
                action: 'recycle',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            },
            {
                title: '内发：电子文件-查询/下载',
                action: 'grant',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            },
            {
                title: '内发：电子文件-查询/下载',
                action: 'recycle',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            },
            {
                title: '外发：电子文件-一次下载',
                action: 'grant',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            }
        ];
    } else if (type.value === 2) {
        return [
            {
                title: '内发：纸质文件-一次下载变更记录',
                action: 'grant',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            },
            {
                title: '内发：纸质文件-一次下载变更记录',
                action: 'recycle',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            }
        ];
    } else {
        return [
            {
                title: '借阅记录',
                action: 'grant',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            },
            {
                title: '借阅记录',
                action: 'recycle',
                approvers: [
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
                    { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
                ]
            }
        ];
    }
});

onMounted(() => {
    console.log(id.value, type.value);
});
</script>

<style scoped lang="less">
.approval-process {
    font-size: 15px;
    .approval-section {
        margin-bottom: 24px;
    }
    .approval-title {
        font-weight: bold;
        margin-bottom: 8px;
        .action {
            margin-left: 16px;
        }
    }
    .approval-content {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    .approval-row {
        display: flex;
        gap: 12px;
        margin-bottom: 2px;
    }
    .label {
        min-width: 56px;
        color: #333;
    }
    .value {
        min-width: 60px;
        color: #000;
    }
}
</style>
