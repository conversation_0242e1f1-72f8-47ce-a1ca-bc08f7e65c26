<template>
    <div class="distribute-file pr-10px">
        <n-grid :cols="2" :x-gap="10" :y-gap="10">
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：电子文件 - 查询</span>
                <grant-recycle
                    :tree-data="personTreeData"
                    :selected-ids="['111']"
                    :approving-ids="['3322']"
                    :selected-list="checkTreeData"
                    @grant="(keys: string[]) => onTransfer(1, keys)"
                    @recycle="(keys: string[]) => onBack(1, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：电子文件 - 查询/下载</span>
                <grant-recycle
                    :tree-data="personTreeData"
                    :selected-list="checkTreeData"
                    @grant="(keys: string[]) => onTransfer(2, keys)"
                    @recycle="(keys: string[]) => onBack(2, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：纸质文件 - 一次下载</span>
                <grant-recycle
                    :tree-data="personTreeData"
                    :selected-list="checkTreeData"
                    @grant="(keys: string[]) => onTransfer(3, keys)"
                    @recycle="(keys: string[]) => onBack(3, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">外发：电子文件 - 一次下载</span>
                <grant-recycle
                    :tree-data="personTreeData"
                    :selected-list="checkTreeData"
                    @grant="(keys: string[]) => onTransfer(4, keys)"
                    @recycle="(keys: string[]) => onBack(4, keys)"
                />
            </n-grid-item>
        </n-grid>
        <div class="flex-v items-start mt-10px">
            <span class="text-14px font-500 mt-20px">发放回收记录</span>
            <n-data-table
                :columns="operateColumns"
                :data="operateData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #in_query="{ row }">
                    <span v-if="row.in_query.length > 0">{{ row.in_query.join('、') }}</span>
                    <span v-else>--</span>
                </template>
                <template #in_query_download="{ row }">
                    <span v-if="row.in_query_download.length > 0">{{ row.in_query_download.join('、') }}</span>
                    <span v-else>--</span>
                </template>
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 1)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">内发：纸质文件-一次下载变更记录</span>
            <n-data-table
                :columns="changeColumns"
                :data="changeData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #status="{ row }">
                    <n-tag :type="row.status === '已回收' ? 'success' : 'error'" size="small" :bordered="false">{{
                        row.status
                    }}</n-tag>
                </template>
                <template #disposeStatus="{ row }">
                    <n-tag
                        :type="row.disposeStatus === '已处置' ? 'success' : 'error'"
                        size="small"
                        :bordered="false"
                        >{{ row.disposeStatus }}</n-tag
                    >
                </template>
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 2)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">外发：电子文件-一次下载变更记录</span>
            <n-data-table
                :columns="outDownloadColumns"
                :data="outDownloadData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #isDownload="{ row }">
                    <n-tag :type="row.isDownload ? 'success' : 'error'" size="small" :bordered="false">{{
                        row.isDownload ? '是' : '否'
                    }}</n-tag>
                </template>
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 2)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">借阅记录</span>
            <n-data-table
                :columns="borrowColumns"
                :data="borrowData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 3)">详情</n-button>
                </template>
            </n-data-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { NTag, PaginationProps } from 'naive-ui';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';

const props = withDefaults(
    defineProps<{
        id?: string;
    }>(),
    {
        id: ''
    }
);
const emit = defineEmits(['update:id']);
const { id } = useVModels(props, emit);

const personTreeData = ref([
    {
        label: '人事部',
        key: '11',
        children: [{ label: '张三', key: '111' }]
    },
    {
        label: '财务部',
        key: '22',
        children: []
    },
    {
        label: '行政部',
        key: '33',
        children: [
            {
                label: '行政一部',
                key: '331',
                children: [{ label: '王五', key: '3311' }]
            },
            {
                label: '行政二部',
                key: '332',
                children: [{ label: '赵六', key: '3322' }]
            }
        ]
    }
]);

const checkTreeData = ref([{ label: '张三', key: '111' }]);

const onTransfer = (type: number, keys: string[]) => {
    console.log('发放人员:', type, keys);
    switch (type) {
        case 1:
            window.$message.info(`内发：电子文件 - 查询，发放人员：${keys.join(',')}`);
            break;
        case 2:
            window.$message.info(`内发：电子文件 - 查询/下载，发放人员：${keys.join(',')}`);
            break;
        case 3:
            window.$message.info(`内发：纸质文件 - 一次下载，发放人员：${keys.join(',')}`);
            break;
        case 4:
            window.$message.info(`外发：电子文件 - 一次下载，发放人员：${keys.join(',')}`);
            break;
    }
};

const onBack = (type: number, keys: string[]) => {
    console.log('回收人员:', type, keys);
    switch (type) {
        case 1:
            window.$message.info(`内发：电子文件 - 查询，回收人员：${keys.join(',')}`);
            break;
        case 2:
            window.$message.info(`内发：电子文件 - 查询/下载，回收人员：${keys.join(',')}`);
            break;
        case 3:
            window.$message.info(`内发：纸质文件 - 一次下载，回收人员：${keys.join(',')}`);
            break;
        case 4:
            window.$message.info(`外发：电子文件 - 一次下载，回收人员：${keys.join(',')}`);
            break;
    }
};

/**
 * 表格配置
 */
const pagination = ref<PaginationProps>({
    pageSize: 5,
    prefix: ({ itemCount }) => `共 ${itemCount} 条`
});

/**
 * 发放回收记录
 */
const operateColumns = ref<TableColumns>([
    {
        title: '操作时间',
        key: 'time',
        align: 'center',
        width: 180
    },
    {
        title: '内发：电子文件-查询',
        key: 'in_query',
        align: 'center',
        ellipsis: {
            tooltip: true
        },
        render(row: any) {
            return Array.isArray(row.in_query) ? row.in_query.join('、') : row.in_query;
        }
    },
    {
        title: '内发：电子文件-查询/下载',
        key: 'in_query_download',
        align: 'center',
        ellipsis: {
            tooltip: true
        },
        render(row: any) {
            return Array.isArray(row.in_query_download) ? row.in_query_download.join('、') : row.in_query_download;
        }
    },
    {
        title: '变更人',
        key: 'changer',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const operateData = ref([]);
const getOperateData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 20, noPage: true },
        data: {
            time: '@date("yyyy-MM-dd HH:mm:ss")',
            'in_query|1-3': ['@cname'],
            'in_query_download|1-3': ['@cname'],
            'out_download|1-3': ['@cname'],
            changer: '@cname'
        }
    });
    operateData.value = res.data.data;
};

/**
 * 内发：纸质文件-一次下载变更记录
 */
const changeColumns = ref<TableColumns>([
    {
        title: '纸质文件接收人',
        key: 'receiver',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '纸质文件状态',
        key: 'status',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '文件处置状态',
        key: 'disposeStatus',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放人',
        key: 'issuer',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放申请时间',
        key: 'issueTime',
        align: 'center',
        width: 180
    },
    {
        title: '回收人',
        key: 'recycler',
        align: 'center',
        width: 180
    },
    {
        title: '回收申请时间',
        key: 'recycleTime',
        align: 'center',
        width: 180
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const changeData = ref([]);
const getChangeData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            time: '@date("yyyy-MM-dd HH:mm:ss")',
            receiver: '@cname',
            'status|1': ['已回收'],
            'disposeStatus|1': ['已处置', '未处置', '处置中'],
            issuer: '@cname',
            changer: '@cname',
            issueTime: '@date("yyyy-MM-dd HH:mm:ss")',
            recycler: '@cname',
            recycleTime: '@date("yyyy-MM-dd HH:mm:ss")'
        }
    });
    changeData.value = res.data.data;
};

/**
 * 外发：电子文件-一次下载变更记录
 */
const outDownloadColumns = ref<TableColumns>([
    {
        title: '电子文件接收方',
        key: 'receiver',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放人',
        key: 'issuer',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放申请时间',
        key: 'issueTime',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '是否下载',
        key: 'isDownload',
        align: 'center',
        width: 80
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const outDownloadData = ref([]);
const getOutDownloadData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            receiver: '@cname',
            issuer: '@cname',
            issueTime: '@date("yyyy-MM-dd HH:mm:ss")',
            isDownload: '@boolean',
            changer: '@cname'
        }
    });
    outDownloadData.value = res.data.data;
};

/**
 * 借阅记录
 */
const borrowColumns = ref<TableColumns>([
    {
        title: '操作时间',
        key: 'time',
        align: 'center',
        width: 180
    },
    {
        title: '借阅人',
        key: 'borrower',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '借阅期限',
        key: 'borrowPeriod',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const borrowData = ref([]);

const getBorrowData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            time: '@date("yyyy-MM-dd HH:mm:ss")',
            borrower: '@cname',
            borrowPeriod: '@date("yyyy-MM-dd") 至 @date("yyyy-MM-dd")',
            changer: '@cname'
        }
    });
    borrowData.value = res.data.data;
};

const handleApproval = (row: any, type: number) => {
    $alert.dialog({
        title: '审批详情',
        content: import('./distribute-file-approval-process.vue'),
        width: '50%',
        props: {
            id: row.id,
            type
        }
    });
};

onMounted(() => {
    console.log(id.value);
    getOperateData();
    getChangeData();
    getOutDownloadData();
    getBorrowData();
});
</script>

<style scoped lang="less"></style>
