<template>
    <alert-content :buttons="buttons">
        <n-form
            class="mt-2"
            ref="formRef"
            :model="params"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            label-width="110px"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="12" :x-gap="16">
                <n-form-item-gi :span="12" label="申请人">
                    <n-input v-model:value="params.applicant" readonly disabled />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="申请日期">
                    <n-input :value="dayjs(params.applyDate).format('YYYY-MM-DD')" readonly disabled />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="发放类型" path="distributeType" required>
                    <n-select
                        v-model:value="params.distributeType"
                        :options="issuanceTypeOptions"
                        placeholder="请选择发放类型"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="文件类型" path="fileType" required>
                    <n-select
                        v-model:value="params.fileType"
                        :options="fileTypeOptions"
                        placeholder="请选择文件类型"
                        clearable
                        @update:value="onFileTypeChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="文件类别" path="typeDictNodeId" required>
                    <SelectTreeDictionary
                        v-model:value="params.typeDictNodeId"
                        placeholder="请选择文件类别"
                        clearable
                        filterable
                        :disabled="!params.fileType"
                        :need-path-info="true"
                        @change="onTypeDictNodeIdChange"
                        :params="params.fileType === 1 ? 'internal' : params.fileType === 2 ? 'external' : ''"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="发放原因" path="reasonDictNodeId" required>
                    <n-select
                        v-model:value="params.reasonDictNodeId"
                        :options="reasonOptions"
                        placeholder="请选择发放原因"
                        clearable
                        @update:value="onReasonChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi
                    v-if="params.reasonDictNodeId === '其他'"
                    :span="24"
                    label="其他原因"
                    path="otherReason"
                    required
                >
                    <n-input
                        v-model:value="params.otherReason"
                        maxlength="50"
                        placeholder="请输入其他原因"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="希望发放日期" path="wishDistributeDate">
                    <n-date-picker
                        v-model:value="params.wishDistributeDate"
                        type="date"
                        placeholder="请选择希望发放日期"
                        clearable
                        format="yyyy-MM-dd"
                        style="width: 100%"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="24" path="distributeList">
                    <div class="flex-v w-100%">
                        <span class="ml-32px mb-10px required-field">发放清单</span>
                        <distribution-list-vxe-table
                            ref="distributionListVxeTableRef"
                            v-model="params.distributeList"
                            :fileType="params.fileType ?? undefined"
                            :fileCategory="params.typeDictNodeId ?? undefined"
                            :issuanceType="params.distributeType ?? undefined"
                        />
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import type { ButtonsConfig } from '@/components/alert-content.vue';
import DistributionListVxeTable from '../components/distribution-list-vxe-table.vue';

const store = useStore();
const formRef = ref<FormInst>();
const pathString = ref('');
/**
 * 底部按钮配置
 * 便于使用组件自带emit事件以及关闭弹窗功能
 */
const buttons: ButtonsConfig = {
    save: {
        text: '提交',
        onClick: () => onSubmit()
    },
    extra: {
        saveTemp: {
            text: '暂存',
            type: 'success',
            onClick: () => onSaveTemp()
        }
    }
};

const props = defineProps<{
    oId: string;
    row: any;
}>();

const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];
const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];
const reasonOptions = [
    { label: '新员工入职', value: '新员工入职' },
    { label: '岗位/职责调整', value: '岗位/职责调整' },
    { label: '文件版本更新', value: '文件版本更新' },
    { label: '新增业务/流程实施', value: '新增业务/流程实施' },
    { label: '跨部门协作需求', value: '跨部门协作需求' },
    { label: '其他', value: '其他' }
];

const params = reactive({
    applicant: store.userInfo.nickname || '自动生成',
    applyDate: dayjs().valueOf(),
    distributeType: null,
    fileType: null,
    typeDictNodeId: null,
    reasonDictNodeId: null,
    otherReason: null,
    wishDistributeDate: null,
    distributeList: []
});
const initFormData = () => {
    if (props.row && props.row !== false && props.oId) {
        // 编辑模式
        Object.assign(params, {
            applicant: props.row.applicant || store.userInfo.nickname || '自动生成',
            applyDate: props.row.applyDate || dayjs().valueOf(),
            distributeType: props.row.distributeType != null ? Number(props.row.distributeType) : null,
            fileType: props.row.fileType != null ? Number(props.row.fileType) : null,
            typeDictNodeId: props.row.typeDictNodeId,
            reasonDictNodeId: props.row.reasonDictNodeId,
            otherReason: props.row.otherReason,
            wishDistributeDate: props.row.wishDistributeDate ? Number(props.row.wishDistributeDate) : null,
            distributeList: props.row.distributeList || []
        });
    }
};

const rules = computed<FormRules>(() => {
    return {
        distributeType: { required: true, type: 'number', message: '请选择发放类型', trigger: 'change' },
        fileType: { required: true, type: 'number', message: '请选择文件类型', trigger: 'change' },
        typeDictNodeId: { required: true, message: '请选择文件类别', trigger: 'change' },
        reasonDictNodeId: { required: true, message: '请选择发放原因', trigger: 'change' },
        otherReason: { required: params.reasonDictNodeId === '其他', message: '请输入其他原因', trigger: 'blur' },
        distributeList: [
            {
                required: true,
                validator: () => {
                    if (!params.distributeList.length) {
                        return new Error('请填写发放清单');
                    }
                    return true;
                }
            }
        ]
    };
});

const onFileTypeChange = () => {
    params.typeDictNodeId = null;
};

const onReasonChange = (val: string) => {
    if (val !== '其他') {
        params.otherReason = null;
    }
};

const onTypeDictNodeIdChange = (val: any, pathInfo: any) => {
    pathString.value = pathInfo.pathString;
};

const onSaveTemp = () => {
    formRef.value?.validate().then(() => {
        console.log(params);
    });
};

function getBusinessId(params: Record<string, any>): { businessId: string; error?: string } {
    // 遍历所有清单和权限，确保类型一致，否则报错
    let detectedId = '';
    for (const item of params.distributeList) {
        for (const perm of (item.permissions || [])) {
            const distributeType = params.distributeType;
            const filePermission = perm.filePermission;
            let currentId = '';
            if (distributeType === 1) {
                if (filePermission === '查阅') currentId = 'FILE_INTERNAL_READ';
                else if (filePermission === '查阅/下载') currentId = 'FILE_INTERNAL_READANDDOWNLOAD';
                else if (filePermission === '一次下载') currentId = 'FILE_INTERNAL_DOWNLOAD';
            } else if (distributeType === 2) {
                if (filePermission === '一次下载') currentId = 'FILE_EXTERNAL_DOWNLOAD';
            }
            if (!currentId) return { businessId: '', error: '存在无法识别的流程类型，请检查权限配置' };
            if (!detectedId) detectedId = currentId;
            else if (detectedId !== currentId) return { businessId: '', error: '多条权限类型不一致，请拆分申请' };
        }
    }
    return { businessId: detectedId };
}

const onSubmit = async () => {
    await formRef.value?.validate();
    await distributionListVxeTableRef.value?.tableValid();
    const { businessId, error } = getBusinessId(params);
    if (error || !businessId) {
        window.$message.error(error || '无法识别流程类型');
        return;
    }
    const formContent = JSON.stringify({
        businessId,
        version: '1.0.0',
        data: {
            data: { ...params, category: pathString.value },
            nickname: store.userInfo.nickname,
            reason: "发放文件流程申请"
        }
    });
    console.log(params);
    await new Promise((resolve) => {
        window.$dialog.warning({
            title: '确认提示',
            content: `确认后将发起审批流程，是否确认？`,
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
                await $hooks.useApprovalProcess(businessId as string, formContent);
                window.$message.success('提交成功');
                resolve(true);
            }
        });
    });
};

// 监听 props 变化，重新初始化表单数据
watch(
    () => props.row,
    () => {
        initFormData();
    },
    { immediate: true, deep: true }
);

watch(
    () => props.oId,
    () => {
        initFormData();
    },
    { immediate: true }
);

// 组件挂载时也尝试初始化数据
onMounted(() => {
    initFormData();
});

const distributionListVxeTableRef = ref();
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>
