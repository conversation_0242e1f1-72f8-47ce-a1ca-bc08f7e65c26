<template>
    <alert-content :showDefaultButtons="false">
        <n-descriptions label-placement="left" :column="2" size="small" class="mb-2">
            <n-descriptions-item label="文件名称">{{ fileName }}</n-descriptions-item>
            <n-descriptions-item label="文件编号">{{ fileNo }}</n-descriptions-item>
        </n-descriptions>
        <p>处置记录</p>
        <n-search-table-page
            :data-table-props="{
                columns,
                data: records,
                size: 'small',
                bordered: true,
                pagination: false,
                scrollX: 1200
            }"
            :search-props="{ show: false }"
            :table-props="{ showPagination: false }"
        />
    </alert-content>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import AlertContent from '@/components/alert-content.vue';

const props = defineProps<{
    id?: string;
    fileName?: string;
    fileNo?: string;
}>();

const fileName = ref(props.fileName || '');
const fileNo = ref(props.fileNo || '');
const records = ref<any[]>([]);

const columns = [
    { title: '序号', key: 'index', width: 60, render: (_: any, idx: number) => idx + 1 },
    { title: '交还人', key: 'handoverPerson', width: 120 },
    { title: '交还日期', key: 'handoverDate', width: 120 },
    { title: '回收人', key: 'recyclePerson', width: 120 },
    { title: '回收日期', key: 'recycleDate', width: 120 },
    { title: '处置人', key: 'disposalPerson', width: 120 },
    { title: '处置日期', key: 'disposalDate', width: 120 },
    { title: '处置方式', key: 'disposalType', width: 120 }
];

onMounted(() => {
    // TODO: 替换为实际接口调用
    if (!fileName.value) {
        fileName.value = `示例文件.docx${props.id}`;
    }
    if (!fileNo.value) {
        fileNo.value = 'DOC-2024-001';
    }
    records.value = [
        {
            handoverPerson: '张三',
            handoverDate: '2024-05-01',
            recyclePerson: '李四',
            recycleDate: '2024-05-02',
            disposalPerson: '王五',
            disposalDate: '2024-05-03',
            disposalType: '销毁'
        },
        {
            handoverPerson: '赵六',
            handoverDate: '2024-05-04',
            recyclePerson: '孙七',
            recycleDate: '2024-05-05',
            disposalPerson: '周八',
            disposalDate: '2024-05-06',
            disposalType: '归档'
        }
    ];
});
</script>

<style scoped>
.mb-2 {
    margin-bottom: 16px;
}
</style>
