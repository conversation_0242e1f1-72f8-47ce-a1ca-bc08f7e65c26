<template>
    <alert-content :on-default-save="onSubmit" :buttons="buttons">
        <n-form
            class="mt-2"
            ref="formRef"
            label-align="right"
            :model="params"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="90"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="回收人:" :span="12">
                    <n-input disabled :value="userInfo.name" />
                </n-form-item-gi>
                <n-form-item-gi label="回收日期" :span="12">
                    <n-input disabled :value="today" />
                </n-form-item-gi>
                <n-form-item-gi label="发放类型" :span="12">
                    <n-input disabled :value="row.issuanceType" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" :span="12">
                    <n-input disabled :value="row.fileType" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" :span="12">
                    <n-input disabled :value="row.category" />
                </n-form-item-gi>
                <n-form-item-gi label="发放原因" :span="12">
                    <n-input disabled :value="row.reason" />
                </n-form-item-gi>
                <n-form-item-gi label="回收原因" path="reason" :span="12">
                    <n-select
                        v-model:value="params.reason"
                        :options="reasonOptions"
                        placeholder="请选择回收原因"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="params.reason === '其他'" label="其他原因" path="otherReason" :span="12">
                    <n-input
                        v-model:value="params.otherReason"
                        maxlength="50"
                        placeholder="请输入其他原因"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="24" path="checked">
                    <div class="flex-v w-100%">
                        <span class="ml-12px mb-10px text-14px required-field">发放清单</span>
                        <recycle-list-vxe-table ref="recycleListVxeTableRef" :id="row.id" v-model="params.checked" />
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormInst, FormRules } from 'naive-ui';
import AlertContent, { ButtonsConfig } from '@/components/alert-content.vue';
import RecycleListVxeTable from '../components/recycle-list-table.vue';

const buttons: ButtonsConfig = {
    save: {
        text: '回收'
    }
};

const { row } = defineProps<{ row: any }>();
const userInfo = { name: '当前用户' };
const today = new Date().toISOString().slice(0, 10);

// 原因选项
const reasonOptions = [
    { label: '员工离职/调离', value: '员工离职/调离' },
    { label: '岗位/职责调整', value: '岗位/职责调整' },
    { label: '文件版本更新', value: '文件版本更新' },
    { label: '文件正式作废', value: '文件正式作废' },
    { label: '文件过期失效', value: '文件过期失效' },
    { label: '错误发放纠正', value: '错误发放纠正' },
    { label: '定期清理', value: '定期清理' },
    { label: '其他', value: '其他' }
];

// 表单数据
const params = reactive({
    reason: null,
    otherReason: null,
    checked: []
});

const formRef = ref<FormInst>();

// 表单验证规则
const rules = computed<FormRules>(() => ({
    reason: [{ required: true, message: '请选择回收原因', trigger: ['blur', 'change'] }],
    otherReason: [
        {
            required: params.reason === '其他',
            message: '请输入其他原因',
            trigger: 'blur'
        }
    ],
    checked: [
        {
            required: true,
            trigger: 'blur',
            validator: () => {
                if (!params.checked.length) {
                    return new Error('请至少选择一个文件的人员进行回收');
                }
                return true;
            }
        }
    ]
}));

// 提交表单
const onSubmit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            const submitData = {
                reason: params.reason,
                otherReason: params.reason === '其他' ? params.otherReason : null,
                files: params.checked
            };

            console.log('提交数据：', submitData);

            await new Promise((resolve) => {
                window.$dialog.info({
                    title: '确认提示',
                    content: '确认后将发起审批流程，是否确认？',
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: async () => {
                        window.$message.success('回收流程已发起');
                        $alert.dialog.close();
                        resolve(true);
                    }
                });
            });
        })
        .catch((errors) => {
            if (errors && errors[0] && errors[0][0]) {
                window.$message.error(errors[0][0].message);
            } else {
                window.$message.error('请完善表单信息');
            }
            return Promise.reject();
        });
};
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>
