<template>
    <div class="distribution-list-vxe-table">
        <vxe-table
            ref="tableRef"
            :data="flatTableData"
            :border="true"
            :span-method="spanMethod"
            auto-resize
            :edit-rules="validRules"
            :valid-config="{ showMessage: false }"
            :edit-config="{ trigger: 'click', mode: 'cell', showStatus: false, enabled: canEditTable }"
            @edit-closed="handleEditClosed"
            :tooltip-config="tooltipConfig"
        >
            <vxe-column field="fileName" title="文件名称" width="160" :edit-render="{}">
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.fileName"
                        :options="getFileNameOptions()"
                        filterable
                        clearable
                        @update:value="(val) => onFileNameSelect(row, val)"
                        placeholder="选择文件名称"
                        style="width: 100%"
                    />
                </template>
            </vxe-column>
            <vxe-column field="fileNo" title="文件编号" width="140" :edit-render="{}">
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.fileNo"
                        :options="getFileNoOptions()"
                        filterable
                        clearable
                        @update:value="(val) => onFileNoSelect(row, val)"
                        placeholder="选择文件编号"
                        style="width: 100%"
                    />
                </template>
            </vxe-column>
            <vxe-column field="version" title="版本/版次" width="120" :edit-render="{}">
                <template #edit="{ row }">
                    <n-input
                        v-model:value="row.version"
                        size="small"
                        placeholder="请输入版本/版次"
                        readonly
                        disabled
                    ></n-input>
                </template>
            </vxe-column>
            <vxe-column
                field="fileForm"
                title="文件形式"
                min-width="140"
                :edit-render="{}"
                :formatter="fileFormFormatter"
            >
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.fileForm"
                        size="small"
                        clearable
                        :options="getFileFormOptions(row)"
                        placeholder="请选择文件形式"
                        :disabled="getFileFormOptions(row).length === 0"
                    ></n-select>
                </template>
            </vxe-column>
            <vxe-column field="filePermission" title="文件权限" min-width="120" :edit-render="{}" :formatter="filePermissionFormatter">
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.filePermission"
                        size="small"
                        :options="getFilePermissionOptions(row)"
                        placeholder="请选择文件权限"
                        :disabled="getFilePermissionOptions(row).length === 0"
                    ></n-select>
                </template>
            </vxe-column>
            <vxe-column field="receiver" title="接收方" width="120" :edit-render="{}">
                <template #edit="{ row }">
                    <n-input
                        v-model:value="row.receiver"
                        size="small"
                        placeholder="请输入接收方"
                        :disabled="!shouldShowReceiver(row)"
                    ></n-input>
                </template>
            </vxe-column>
            <vxe-column field="receivedBy" title="接收人" width="180" :formatter="() => ''" show-overflow="tooltip">
                <template #default="{ row }">
                    <n-button size="small" @click="openPersonModal(row)"> 选择接收人 </n-button>
                </template>
            </vxe-column>
            <vxe-column field="actions" title="操作" width="145">
                <template #default="{ row }">
                    <n-space :size="5" justify="center">
                        <n-button
                            v-if="row.permIndex === itemList[row.fileIndex].permissions.length - 1"
                            type="primary"
                            size="tiny"
                            @click="addPermission(row.fileIndex)"
                        >
                            增加权限
                        </n-button>
                        <n-button
                            v-if="itemList[row.fileIndex].permissions.length > 1"
                            type="warning"
                            size="tiny"
                            @click="removePermission(row.fileIndex, row.permIndex)"
                        >
                            删除权限
                        </n-button>
                        <n-button
                            v-if="row.permIndex === 0"
                            type="error"
                            size="tiny"
                            @click="removeFile(row.fileIndex)"
                        >
                            删除清单
                        </n-button>
                    </n-space>
                </template>
            </vxe-column>
        </vxe-table>
        <div class="table-footer">
            <n-button status="primary" size="tiny" @click="addFile">增加一行</n-button>
        </div>
        <!-- 人员选择弹窗 -->
        <n-modal v-model:show="showPersonModal" preset="card" title="选择接收人" :style="{ width: '600px' }">
            <n-transfer
                v-model:value="selectedPersonIds"
                :options="allPersonOptions"
                :source-filterable="true"
                :target-filterable="true"
                :source-filter-placeholder="'请输入姓名搜索'"
                :target-filter-placeholder="'请输入姓名搜索'"
            />
            <template #footer>
                <n-space justify="center">
                    <n-button @click="showPersonModal = false">取消</n-button>
                    <n-button type="primary" @click="confirmPersonSelection">确定</n-button>
                </n-space>
            </template>
        </n-modal>
    </div>
</template>

<script setup lang="ts">
import { RowVO } from '@/api/sass/api/v1/dict';
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import { VxeTable, VxeColumn, VxeTablePropTypes } from 'vxe-table';
import useStore from '@/store/modules/main';

interface PersonItem {
    userId: string;
    userName: string;
}
interface PermissionItem {
    fileForm: string;
    filePermission: string;
    receiver: string;
    receivedBy: PersonItem[];
}
interface IssuanceItem {
    id: string;
    fileName: string;
    fileNo: string;
    version: string;
    permissions: PermissionItem[];
}

const props = defineProps<{
    modelValue: IssuanceItem[];
    issuanceType?: number | null;
    fileType?: number | null;
    fileCategory?: string | null;
}>();
const emit = defineEmits(['update:modelValue']);

// 文件形式
const fileFormOptions = [
    { label: '电子文件', value: 1 },
    { label: '纸质文件', value: 2 }
];

const validRules = ref<VxeTablePropTypes.EditRules<RowVO>>({
    fileName: [{ required: true, message: '请输入文件名称' }],
    fileNo: [{ required: true, message: '请输入文件编号' }],
    version: [{ required: true, message: '请输入版本/版次' }],
    fileForm: [{ required: true, message: '请选择文件形式' }],
    filePermission: [{ required: true, message: '请选择文件权限' }],
    receivedBy: [
        {
            required: true,
            validator({ cellValue }) {
                if (!Array.isArray(cellValue) || cellValue.length === 0) {
                    return new Error('请选择接收人');
                }
            }
        }
    ]
});

const itemList = ref<IssuanceItem[]>(props.modelValue || []);
// 监听 props 变化，条件变动时清空 itemList
watch(
    [() => props.issuanceType, () => props.fileType, () => props.fileCategory],
    ([newIssuanceType, newFileType, newFileCategory], [oldIssuanceType, oldFileType, oldFileCategory]) => {
        if (newIssuanceType !== oldIssuanceType || newFileType !== oldFileType || newFileCategory !== oldFileCategory) {
            itemList.value = [];
            update();
        }
    }
);

function update() {
    emit('update:modelValue', itemList.value);
}

/**
 * 处理单元格编辑完成事件
 * @param {Object} params - 编辑完成事件参数
 */
function handleEditClosed({ row, column }: { row: any; column: { field: string } }) {
    const { field } = column;
    const { fileIndex, permIndex } = row;

    // 根据字段类型更新对应的数据
    if (['fileName', 'fileNo', 'version'].includes(field)) {
        // 更新文件基本信息
        (itemList.value[fileIndex] as unknown as { [key: string]: any })[field] = row[field];
    } else if (['fileForm', 'filePermission', 'receiver', 'receivedBy'].includes(field)) {
        if (field === 'receivedBy') {
            (itemList.value[fileIndex].permissions[permIndex] as { [key: string]: any })[field] =
                Array.isArray(row[field]) && row[field].length > 0
                    ? row[field].map((p: any) => ({
                          userId: p.userId,
                          userName: p.userName
                      }))
                    : [];
        } else {
            (itemList.value[fileIndex].permissions[permIndex] as { [key: string]: any })[field] = row[field];
        }
    }

    update();
}

/**
 * 添加新文件
 */
function addFile() {
    itemList.value.push({
        id: '',
        fileName: '',
        fileNo: '',
        version: '',
        permissions: [
            {
                fileForm: '',
                filePermission: '',
                receiver: '',
                receivedBy: []
            }
        ]
    });
    update();
}

/**
 * 删除文件
 * @param {number} fileIndex - 文件索引
 */
function removeFile(fileIndex: number) {
    itemList.value.splice(fileIndex, 1);
    update();
}

/**
 * 添加权限
 * @param {number} fileIndex - 文件索引
 */
function addPermission(fileIndex: number) {
    itemList.value[fileIndex].permissions.push({
        fileForm: '',
        filePermission: '',
        receiver: '',
        receivedBy: []
    });
    update();
}

/**
 * 删除权限
 * @param {number} fileIndex - 文件索引
 * @param {number} permIndex - 权限索引
 */
function removePermission(fileIndex: number, permIndex: number) {
    itemList.value[fileIndex].permissions.splice(permIndex, 1);
    if (itemList.value[fileIndex].permissions.length === 0) {
        removeFile(fileIndex);
    } else {
        update();
    }
}
const flatTableData = computed(() => {
    const arr: any[] = [];
    itemList.value.forEach((file, fileIndex) => {
        file.permissions.forEach((perm, permIndex) => {
            arr.push({
                fileIndex,
                permIndex,
                id: file.id,
                fileName: file.fileName,
                fileNo: file.fileNo,
                version: file.version,
                fileForm: perm.fileForm,
                filePermission: perm.filePermission,
                receiver: perm.receiver,
                receivedBy:
                    Array.isArray(perm.receivedBy) &&
                    perm.receivedBy.length > 0 &&
                    typeof perm.receivedBy[0] === 'object'
                        ? perm.receivedBy
                        : (perm.receivedBy || []).map((p: any) => ({ id: p, name: p })),
                issuanceType: props.issuanceType || ''
            });
        });
    });
    return arr;
});

// 合并单元格规则
function spanMethod({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) {
    // 0,1,2列合并
    const row = flatTableData.value[rowIndex];
    const file = itemList.value[row.fileIndex];
    const permIndex = row.permIndex;
    const permCount = file.permissions.length;
    if ([0, 1, 2].includes(columnIndex)) {
        if (permIndex === 0) {
            return { rowspan: permCount, colspan: 1 };
        } else {
            return { rowspan: 0, colspan: 0 };
        }
    }
    return { rowspan: 1, colspan: 1 };
}

// 效验
const tableRef = ref();
const tableValid = async () => {
    const $table = tableRef.value;
    if ($table) {
        const errMap = await $table.validate(true);
        if (errMap) {
            // 获取第一个错误信息

            const firstKey = Object.keys(errMap)[0];
            if (firstKey && errMap[firstKey] && errMap[firstKey].length > 0) {
                const firstError = errMap[firstKey][0];
                throw [
                    [
                        {
                            message: firstError.rule?.$options?.message || firstError.rule?.$options?.content
                        }
                    ]
                ];
            }
        }
        return null;
    }
    return null;
};

// 3. shouldShowReceiver 判断逻辑
function shouldShowReceiver(row: any) {
    return row.issuanceType === 2 && row.fileForm === 1 && row.filePermission === 3;
}

// computed: 只有全部条件有值时表格可编辑
const canEditTable = computed(() => {
    return !!(props.issuanceType && props.fileType && props.fileCategory);
});

// 人员选择弹窗相关
const showPersonModal = ref(false);
const selectedPersonIds = ref<string[]>([]);
const currentEditFileIndex = ref(-1);
const currentEditPermIndex = ref(-1);
// 监听 fileName、fileForm、filePermission 变动，清空 receivedBy
watch(
    itemList,
    (newList, oldList) => {
        newList.forEach((file, fileIndex) => {
            file.permissions.forEach((perm, permIndex) => {
                const oldFile = oldList?.[fileIndex];
                const oldPerm = oldFile?.permissions?.[permIndex];
                if (
                    !oldPerm ||
                    file.fileName !== oldFile.fileName ||
                    perm.fileForm !== oldPerm.fileForm ||
                    perm.filePermission !== oldPerm.filePermission
                ) {
                    perm.receivedBy = [];
                }
            });
        });
    },
    { deep: true }
);

const allPersonOptions = ref<any[]>([]);
async function getPersonOptions(params: { fileId: string; fileForm: number; filePermission: number }) {
    console.log(params);

    const res = await api.sass.api.v1.organizationUserInfo.companyList();
    allPersonOptions.value = res.data?.map((item: any) => {
        return {
            label: item.nickname,
            key: item.id,
            value: item.id
        };
    });
}

function openPersonModal(row: any) {
    currentEditFileIndex.value = row.fileIndex;
    currentEditPermIndex.value = row.permIndex;
    // 兼容 receivedBy 结构
    const current = row.receivedBy || [];
    selectedPersonIds.value = current.map((p: any) => p.userId);
    console.log(selectedPersonIds.value, currentEditPermIndex.value, currentEditFileIndex.value);

    // 传递参数调用接口
    const params = {
        fileId: row.id,
        fileForm: row.fileForm,
        filePermission: row.filePermission
    };
    getPersonOptions(params);
    showPersonModal.value = true;
}

function confirmPersonSelection() {
    if (currentEditFileIndex.value >= 0 && currentEditPermIndex.value >= 0) {
        const selectedPersons = allPersonOptions.value
            .filter((option) => selectedPersonIds.value.includes(option.value))
            .map((option) => ({ userId: option.value, userName: option.label }));
        // 赋值为对象数组
        itemList.value[currentEditFileIndex.value].permissions[currentEditPermIndex.value].receivedBy = selectedPersons;
        update();
    }
    showPersonModal.value = false;
}

const store = useStore();
const fileList = ref<any[]>([]);
const fileListLoading = ref(false);

function getOrgTypeForExternal() {
    return store.userInfo.organizationType === 0 ? 1 : 2;
}

async function fetchFileList() {
    fileListLoading.value = true;
    try {
        let list = [];
        if (props.fileType === 1) {
            // 内部文件库
            const res = await $apis.nebula.api.v1.internal.list({
                docCategoryIds: props.fileCategory ? [props.fileCategory] : [],
                status: 3,
                page: 1,
                pageSize: 99999
            });
            list = res.data?.data ?? [];
        } else if (props.fileType === 2) {
            // 外部文件库
            const orgType = getOrgTypeForExternal();
            const res = await $apis.nebula.api.v1.external.getList({
                typeDictionaryNodeIds: props.fileCategory ? [props.fileCategory] : [],
                status: 3,
                orgType,
                page: 1,
                pageSize: 99999
            });
            list = res.data?.data ?? [];
        }
        fileList.value = Array.isArray(list) ? list : [];
    } finally {
        fileListLoading.value = false;
    }
}

watch(
    () => props.fileCategory,
    () => {
        fetchFileList();
    },
    { immediate: true }
);

function getFileNameOptions() {
    return fileList.value.map((file) => ({
        label: file.name,
        value: file.name,
        fileNo: file.no || file.number,
        version: file.versionNo || file.version
    }));
}
function getFileNoOptions() {
    return fileList.value.map((file) => ({
        label: file.no || file.number,
        value: file.no || file.number,
        fileName: file.name,
        version: file.versionNo || file.version
    }));
}
function onFileNameSelect(row: any, selectedValue: string) {
    const selectedFile = fileList.value.find((file) => file.name === selectedValue);
    console.log(selectedFile, row);
    if (selectedFile) {
        row.fileNo = selectedFile.number;
        row.version = selectedFile.version;
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = selectedFile.name;
            itemList.value[fileIndex].fileNo = selectedFile.number;
            itemList.value[fileIndex].version = selectedFile.version;
            itemList.value[fileIndex].id = selectedFile.id;
            update();
        }
    } else {
        row.fileNo = '';
        row.version = '';
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileNo = '';
            itemList.value[fileIndex].version = '';
            itemList.value[fileIndex].id = '';
            update();
        }
    }
}
function onFileNoSelect(row: any, selectedValue: string) {
    const selectedFile = fileList.value.find((file) => (file.no || file.number) === selectedValue);
    if (selectedFile) {
        row.fileName = selectedFile.name;
        row.version = selectedFile.version;
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = selectedFile.name;
            itemList.value[fileIndex].fileNo = selectedFile.number;
            itemList.value[fileIndex].version = selectedFile.version;
            itemList.value[fileIndex].id = selectedFile.id;
            update();
        }
    } else {
        row.fileName = '';
        row.version = '';
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = '';
            itemList.value[fileIndex].version = '';
            itemList.value[fileIndex].id = '';
            update();
        }
    }
}

// formatter for receivedBy
function receivedByFormatter({ cellValue }: { cellValue: any }) {
    if (Array.isArray(cellValue) && cellValue.length > 0) {
        return cellValue.map((p: any) => p.userName || p.name).join('，');
    }
    return '选择接收人';
}

// 1. 文件形式下拉
function getFileFormOptions(row: any) {
    const issuanceType = row.issuanceType || props.issuanceType;
    if (issuanceType === 1) {
        return [
            { label: '电子文件', value: 1 },
            { label: '纸质文件', value: 2 }
        ];
    } else if (issuanceType === 2) {
        return [{ label: '电子文件', value: 1 }];
    }
    return [];
}
// 2. 文件权限下拉
function getFilePermissionOptions(row: any) {
    const issuanceType = row.issuanceType || props.issuanceType;
    const fileForm = row.fileForm;
    if (issuanceType === 1 && fileForm === 1) {
        return [
            { label: '查阅', value: 1 },
            { label: '查阅/下载', value: 2 }
        ];
    } else if (issuanceType === 1 && fileForm === 2) {
        return [{ label: '一次下载', value: 3 }];
    } else if (issuanceType === 2 && fileForm === 1) {
        return [{ label: '一次下载', value: 3 }];
    }
    return [];
}
watch(
    itemList,
    (newList) => {
        newList.forEach((file) => {
            file.permissions.forEach((perm) => {
                // 文件形式
                const formOpts = getFileFormOptions({ ...perm, issuanceType: props.issuanceType });
                if (!formOpts.some((opt) => String(opt.value) === String(perm.fileForm))) {
                    perm.fileForm = '';
                    perm.filePermission = '';
                }
                // 文件权限
                const permOpts = getFilePermissionOptions({ ...perm, issuanceType: props.issuanceType });
                if (!permOpts.some((opt) => String(opt.value) === String(perm.filePermission))) {
                    perm.filePermission = '';
                }
            });
        });
    },
    { deep: true }
);

function fileFormFormatter({ cellValue }: { cellValue: number }) {
    const option = fileFormOptions.find((opt) => opt.value === cellValue);
    return option ? option.label : cellValue;
}
function filePermissionFormatter({ cellValue }: { cellValue: number }) {
    if (cellValue === 1) return '查阅';
    if (cellValue === 2) return '查阅/下载';
    if (cellValue === 3) return '一次下载';
    return cellValue;
}
const tooltipConfig = {
    contentMethod({ column, row }: { column: any; row: any }) {
        if (column.field === 'receivedBy') {
            if (Array.isArray(row.receivedBy) && row.receivedBy.length > 0) {
                return row.receivedBy.map((p: any) => p.userName || p.name).join('，');
            }
            return '未选择接收人';
        }
    }
};

defineExpose({
    tableValid,
    receivedByFormatter,
    tooltipConfig
});
</script>

<style scoped lang="less">
.table-footer {
    margin-top: 12px;
    text-align: left;
}
</style>
