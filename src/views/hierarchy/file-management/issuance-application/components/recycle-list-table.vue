<template>
    <div class="recycle-list-vxe-table">
        <n-data-table
            :columns="columns"
            :data="tableData"
            :row-key="(row) => row.fileId"
            :checked-row-keys="checkedRowKeys"
            @update:checked-row-keys="handleSelectionChange"
            :scroll-x="1600"
            :loading="loading"
        >
            <template #a="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="checkedMap[row.fileId] ? checkedMap[row.fileId].a : []"
                        @update:value="(val) => handleABCChange(row, 'a', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.a" :key="item.value" :span="4">
                                <n-checkbox
                                    :value="item.value"
                                    :label="item.label"
                                    size="small"
                                    :disabled="item.isRecycle"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #b="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="checkedMap[row.fileId] ? checkedMap[row.fileId].b : []"
                        @update:value="(val) => handleABCChange(row, 'b', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.b" :key="item.value" :span="4">
                                <n-checkbox
                                    :value="item.value"
                                    :label="item.label"
                                    size="small"
                                    :disabled="item.isRecycle"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #c="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="checkedMap[row.fileId] ? checkedMap[row.fileId].c : []"
                        @update:value="(val) => handleABCChange(row, 'c', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.c" :key="item.value" :span="4">
                                <n-checkbox
                                    :value="item.value"
                                    :label="item.label"
                                    size="small"
                                    :disabled="item.isRecycle"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #todo="{ row }">
                <n-button @click="showRecycleDialog(row)" size="tiny" type="primary">回收记录</n-button>
            </template>
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
interface TableRow {
    id: string;
    fileId: string;
    fileName: string;
    fileNo: string;
    fileForm: string;
    a: Array<{ label: string; value: string; isRecycle?: boolean }>;
    b: Array<{ label: string; value: string; isRecycle?: boolean }>;
    c: Array<{ label: string; value: string; isRecycle?: boolean }>;
}

interface BackDataItem {
    fileId: string;
    a: string[];
    b: string[];
    c: string[];
}

const props = defineProps<{ modelValue: BackDataItem[]; id: string }>();
const emit = defineEmits(['update:modelValue']);

const columns = ref<any[]>([
    { type: 'selection', width: 50, fixed: 'left' },
    { title: '序号', key: 'key', align: 'center', width: 60, render: (_: any, index: number) => `${index + 1}` },
    { key: 'fileName', title: '文件名称', align: 'center', fixed: 'left', minWidth: 160, ellipsis: { tooltip: true } },
    { key: 'fileNo', title: '文件编号', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'fileForm', title: '版本/版次', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'a', title: '内发:电子文件-查阅', align: 'center', ellipsis: { tooltip: true } },
    { key: 'b', title: '内发:电子文件-查阅/下载', align: 'center', ellipsis: { tooltip: true } },
    { key: 'c', title: '内发:纸质文件-一次下载', align: 'center', ellipsis: { tooltip: true } },
    { key: 'todo', title: '操作', align: 'center', fixed: 'right', width: 100 }
]);

const tableData = ref<TableRow[]>([]);
const checkedMap = ref<Record<string, { a: string[]; b: string[]; c: string[] }>>({});
const checkedRowKeys = ref<string[]>([]);
const loading = ref(false);

const mockData: TableRow[] = [
    {
        id: '1215616',
        fileId: '1215616',
        fileName: '"中一检测"项目-文件控制管理需求文档',
        fileNo: '1234567890',
        fileForm: '2.0',
        a: [
            { label: '李四广', value: '11', isRecycle: true },
            { label: '王五奇', value: '12', isRecycle: true },
            { label: '张三未', value: '13', isRecycle: true },
            { label: '赵六佳', value: '14' },
            { label: '孙七留', value: '15' },
            { label: '周八默', value: '16', isRecycle: true },
            { label: '周九默', value: '17' },
            { label: '周十默', value: '18' },
            { label: '周十一默', value: '19' },
            { label: '周十二默', value: '20' },
            { label: '周十三默', value: '21' },
            { label: '周十四默', value: '22' },
            { label: '周十五默', value: '23' },
            { label: '周十六默', value: '24' },
            { label: '周十七默', value: '25' }
        ],
        b: [
            { label: '张三未', value: '13' },
            { label: '赵六佳', value: '14' }
        ],
        c: [
            { label: '孙七留', value: '15' },
            { label: '周八默', value: '16' }
        ]
    },
    {
        id: '1215617',
        fileId: '1215617',
        fileName: '"中一检测"项目-体系控制管理需求文档',
        fileNo: '1234567890',
        fileForm: '3.0',
        a: [
            { label: '李四广', value: '11' },
            { label: '王五奇', value: '12' }
        ],
        b: [
            { label: '张三未', value: '13' },
            { label: '赵六佳', value: '14' }
        ],
        c: [
            { label: '孙七留', value: '15' },
            { label: '周八默', value: '16' }
        ]
    }
];

const loadData = async () => {
    loading.value = true;
    try {
        // 通过id获取数据
        // xxxx
        tableData.value = mockData;
        initCheckedMap();
    } finally {
        loading.value = false;
    }
};

function initCheckedMap() {
    checkedMap.value = {};
    for (const row of tableData.value) {
        checkedMap.value[row.fileId] = { a: [], b: [], c: [] };
    }
    // 回填 modelValue
    for (const item of props.modelValue || []) {
        if (checkedMap.value[item.fileId]) {
            checkedMap.value[item.fileId].a = [...item.a];
            checkedMap.value[item.fileId].b = [...item.b];
            checkedMap.value[item.fileId].c = [...item.c];
        }
    }
    updateCheckedRowKeys();
}

function updateCheckedRowKeys() {
    checkedRowKeys.value = tableData.value
        .filter((row) => {
            const checked = checkedMap.value[row.fileId];
            return (
                checked &&
                checked.a.length === row.a.length &&
                checked.b.length === row.b.length &&
                checked.c.length === row.c.length &&
                checked.a.length + checked.b.length + checked.c.length > 0
            );
        })
        .map((row) => row.fileId);
}

function handleABCChange(row: TableRow, key: 'a' | 'b' | 'c', val: (string | number)[]) {
    checkedMap.value[row.fileId][key] = val.map(String);
    updateCheckedRowKeys();
    emitBackData();
}

function handleSelectionChange(keys: (string | number)[]) {
    const stringKeys = keys.map(String);
    checkedRowKeys.value = stringKeys;

    for (const row of tableData.value) {
        const isChecked = stringKeys.includes(row.fileId);
        const wasChecked =
            checkedMap.value[row.fileId].a.length === row.a.length &&
            checkedMap.value[row.fileId].b.length === row.b.length &&
            checkedMap.value[row.fileId].c.length === row.c.length &&
            checkedMap.value[row.fileId].a.length +
                checkedMap.value[row.fileId].b.length +
                checkedMap.value[row.fileId].c.length >
                0;
        if (isChecked && !wasChecked) {
            // 选中：全选 a/b/c（过滤掉 isRecycle 为 true 的项）
            checkedMap.value[row.fileId].a = row.a.filter((i) => !i.isRecycle).map((i) => i.value);
            checkedMap.value[row.fileId].b = row.b.filter((i) => !i.isRecycle).map((i) => i.value);
            checkedMap.value[row.fileId].c = row.c.filter((i) => !i.isRecycle).map((i) => i.value);
        } else if (!isChecked && wasChecked) {
            // 取消：清空 a/b/c
            checkedMap.value[row.fileId].a = [];
            checkedMap.value[row.fileId].b = [];
            checkedMap.value[row.fileId].c = [];
        }
        // 其他行不变
    }
    emitBackData();
}

function emitBackData() {
    const backData = Object.entries(checkedMap.value)
        .filter(([, checked]) => checked.a.length || checked.b.length || checked.c.length)
        .map(([fileId, checked]) => ({
            fileId,
            a: checked.a,
            b: checked.b,
            c: checked.c
        }));
    emit('update:modelValue', backData);
}

onMounted(() => {
    loadData();
});

const showRecycleDialog = (row: any) => {
    $alert.dialog({
        title: '回收记录',
        content: import('../models/recycle-record.vue'),
        width: '60%',
        props: { id: row.id }
    });
};
</script>

<style scoped lang="less"></style>
