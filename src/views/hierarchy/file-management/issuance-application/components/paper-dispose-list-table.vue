<template>
    <div class="paper-dispose-list-table">
        <n-data-table
            :columns="columns"
            :data="tableData"
            :row-key="(row) => row.fileId"
            :checked-row-keys="checkedRowKeys"
            @update:checked-row-keys="handleSelectionChange"
            :scroll-x="1400"
            :loading="loading"
        >
            <template #exchangePerson="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="checkedMap[row.fileId] ? checkedMap[row.fileId].exchangePerson : []"
                        @update:value="(val) => handleChange(row, 'exchangePerson', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.exchangePerson" :key="item.value" :span="4">
                                <n-checkbox
                                    :value="item.value"
                                    :label="item.label"
                                    size="small"
                                    :disabled="item.isDispose"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #todo="{ row }">
                <n-button @click="showDisposalRecord(row)" size="tiny" type="primary">处置记录</n-button>
            </template>
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
interface TableRow {
    id: string;
    fileId: string;
    fileName: string;
    fileNo: string;
    version: string;
    fileForm: string;
    issueCount: number;
    recycleCount: number;
    disposeCount: number;
    exchangePerson: Array<{ label: string; value: string; isDispose?: boolean }>;
}

interface BackDataItem {
    fileId: string;
    exchangePerson: string[];
}

const props = defineProps<{ modelValue: BackDataItem[]; id: string }>();
const emit = defineEmits(['update:modelValue']);

const columns = ref<any[]>([
    { type: 'selection', width: 50, fixed: 'left' },
    { title: '序号', key: 'key', align: 'center', width: 60, render: (_: any, index: number) => `${index + 1}` },
    { key: 'fileName', title: '文件名称', align: 'center', fixed: 'left', minWidth: 160, ellipsis: { tooltip: true } },
    { key: 'fileNo', title: '文件编号', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'version', title: '版本/版次', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'fileForm', title: '文件形式', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'issueCount', title: '发放份数', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'recycleCount', title: '回收份数', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'disposeCount', title: '处置份数', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'exchangePerson', title: '交换人', align: 'center', ellipsis: { tooltip: true } },
    { key: 'todo', title: '操作', align: 'center', fixed: 'right', width: 100 }
]);

const tableData = ref<TableRow[]>([]);
const checkedMap = ref<Record<string, { exchangePerson: string[] }>>({});
const checkedRowKeys = ref<string[]>([]);
const loading = ref(false);

const mockData: TableRow[] = [
    {
        id: '1215616',
        fileId: '1215616',
        fileName: '"中一检测"项目-文件控制管理需求文档',
        fileNo: '1234567890',
        version: '2.0',
        fileForm: '电子文件',
        issueCount: 10,
        recycleCount: 5,
        disposeCount: 3,
        exchangePerson: [
            { label: '李四广', value: '11', isDispose: true },
            { label: '王五奇', value: '12', isDispose: true },
            { label: '张三未', value: '13', isDispose: true },
            { label: '赵六佳', value: '14' },
            { label: '孙七留', value: '15' },
            { label: '周八默', value: '16', isDispose: true },
            { label: '周九默', value: '17' },
            { label: '周十默', value: '18' },
            { label: '周十一默', value: '19' },
            { label: '周十二默', value: '20' },
            { label: '周十三默', value: '21' },
            { label: '周十四默', value: '22' },
            { label: '周十五默', value: '23' },
            { label: '周十六默', value: '24' },
            { label: '周十七默', value: '25' }
        ]
    },
    {
        id: '1215617',
        fileId: '1215617',
        fileName: '"中一检测"项目-体系控制管理需求文档',
        fileNo: '1234567890',
        version: '3.0',
        fileForm: '纸质文件',
        issueCount: 10,
        recycleCount: 5,
        disposeCount: 3,
        exchangePerson: [
            { label: '李四广', value: '11' },
            { label: '王五奇', value: '12' }
        ]
    }
];

const loadData = async () => {
    loading.value = true;
    try {
        // 通过id获取数据
        // xxxx
        tableData.value = mockData;
        initCheckedMap();
    } finally {
        loading.value = false;
    }
};

function initCheckedMap() {
    checkedMap.value = {};
    for (const row of tableData.value) {
        checkedMap.value[row.fileId] = { exchangePerson: [] };
    }
    // 回填 modelValue
    for (const item of props.modelValue || []) {
        if (checkedMap.value[item.fileId]) {
            checkedMap.value[item.fileId].exchangePerson = [...item.exchangePerson];
        }
    }
    updateCheckedRowKeys();
}

function updateCheckedRowKeys() {
    checkedRowKeys.value = tableData.value
        .filter((row) => {
            const checked = checkedMap.value[row.fileId];
            return (
                checked &&
                checked.exchangePerson.length === row.exchangePerson.length &&
                checked.exchangePerson.length > 0
            );
        })
        .map((row) => row.fileId);
}

function handleChange(row: TableRow, key: 'exchangePerson', val: (string | number)[]) {
    checkedMap.value[row.fileId][key] = val.map(String);
    updateCheckedRowKeys();
    emitBackData();
}

function handleSelectionChange(keys: (string | number)[]) {
    const stringKeys = keys.map(String);
    checkedRowKeys.value = stringKeys;

    for (const row of tableData.value) {
        const isChecked = stringKeys.includes(row.fileId);
        const wasChecked =
            checkedMap.value[row.fileId].exchangePerson.length === row.exchangePerson.length &&
            checkedMap.value[row.fileId].exchangePerson.length > 0;
        if (isChecked && !wasChecked) {
            // 选中：全选 a/b/c（过滤掉 isDispose 为 true 的项）
            checkedMap.value[row.fileId].exchangePerson = row.exchangePerson.map((i) => i.value);
        } else if (!isChecked && wasChecked) {
            // 取消：清空 a/b/c
            checkedMap.value[row.fileId].exchangePerson = [];
        }
        // 其他行不变
    }
    emitBackData();
}

function emitBackData() {
    const backData = Object.entries(checkedMap.value)
        .filter(([, checked]) => checked.exchangePerson.length)
        .map(([fileId, checked]) => ({
            fileId,
            exchangePerson: checked.exchangePerson
        }));
    emit('update:modelValue', backData);
}

onMounted(() => {
    loadData();
});

const showDisposalRecord = (fileItem: any) => {
    $alert.dialog({
        title: '处置记录',
        content: import('../models/disposal-record.vue'),
        width: '60%',
        props: {
            id: String(fileItem.key),
            fileName: fileItem.fileName,
            fileNo: fileItem.fileNo
        }
    });
};
</script>

<style scoped lang="less"></style>
