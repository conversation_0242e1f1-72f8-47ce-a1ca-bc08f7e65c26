import { install } from 'resize-observer';
if (!window.ResizeObserver) install();

import type { PageMap as PageMapType } from '@/typings';
import { name, version } from '../package.json';

/** 打印版本信息 */
console.log(
    `%c${name} v${version} ${__GIT_INFO__.hash} ${__GIT_INFO__.user} ${__GIT_INFO__.date}`,
    'color: #fff; border-radius: 5px; padding: 10px 25px;background: linear-gradient(315deg, #1fd1f9 0%, #b621fe 74%)'
);

window.$modeConfig = import.meta.env.VITE_CONFIG || '{}';

declare global {
    type PageMap = PageMapType;
    const __GIT_INFO__: { hash: string; user: string; date: string };
    interface Window {
        $modeConfig: string;
    }
}
