alert-content 组件使用说明：

1. 基本用法（使用 onSave 和 onCancel）：

```vue
<alert-content :on-cancel="handleCancel" :on-save="handleSave">
  <div>内容区域</div>
</alert-content>
```

2. 等价的使用方式（使用 buttons 配置）：

```vue
<alert-content
    :buttons="{
        cancel: { onClick: handleCancel },
        save: { onClick: handleSave }
    }"
>
  <div>内容区域</div>
</alert-content>
```

3. 混合使用：

```vue
<alert-content
    :on-cancel="handleCancel"
    :buttons="{
        save: { text: '确认', onClick: handleSave }
    }"
>
  <div>内容区域</div>
</alert-content>
```

4. 垂直布局：

```vue
<alert-content layout="vertical" :on-cancel="handleCancel" :on-save="handleSave">
  <div>内容区域</div>
</alert-content>
```

5. 隐藏特定按钮：

```vue
<alert-content
    :buttons="{
        cancel: { show: false }, // 隐藏取消按钮
        save: { show: false } // 隐藏保存按钮
    }"
>
  <div>内容区域</div>
</alert-content>
```

6. 自定义按钮文本和类型：

```vue
<alert-content
    :buttons="{
        cancel: { text: '关闭', type: 'default' },
        save: { text: '确认', type: 'primary' }
    }"
    :on-cancel="handleCancel"
    :on-save="handleSave"
>
  <div>内容区域</div>
</alert-content>
```

7. 添加额外按钮：

```vue
<alert-content
    :buttons="{
        extra: {
            preview: { text: '预览', type: 'info', onClick: handlePreview }
        }
    }"
    :on-cancel="handleCancel"
    :on-save="handleSave"
>
  <div>内容区域</div>
</alert-content>
```

8. 完全隐藏默认按钮，使用自定义 footer：

```vue
<alert-content :show-default-buttons="false">
  <div>内容区域</div>
  <template #footer>
    <div>自定义按钮</div>
  </template>
</alert-content>
```

9.$alert 使用

```js
$alert.dialog({ 
  props: { 
    onSave: () => init(), // 保存按钮点击回调 
    onCancel: () => init(), // 取消按钮点击回调 
    onClose: () => init() // 弹窗关闭回调 
  }
});
```
