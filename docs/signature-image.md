# 签名图片展示组件（signature-image）说明文档

## 1. 组件简介

`signature-image` 组件用于以高质量方式展示用户签名图片，支持加载、预览、错误与空状态，适配不同尺寸和背景，适用于各类签名展示场景。

---

## 2. 主要功能

- 支持 base64 签名图片的加载与高质量渲染
- 支持自定义宽高、背景色、边框、画质
- 支持点击图片弹出大图预览
- 提供加载中、加载失败、无签名三种状态提示
- 适配高分屏，自动缩放

---

## 3. Props 属性

| 属性名          | 类型                    | 默认值  | 说明                                                     |
| --------------- | ----------------------- | ------- | -------------------------------------------------------- |
| signatureBase64 | string                  | -       | 签名图片 base64 数据（可带 data:image/png;base64, 前缀） |
| width           | number                  | 100     | 展示区域宽度（px）                                       |
| height          | number                  | 40      | 展示区域高度（px）                                       |
| backgroundColor | string                  | #ffffff | 背景色                                                   |
| borderColor     | string                  | #e0e0e0 | 边框颜色                                                 |
| showBorder      | boolean                 | true    | 是否显示边框                                             |
| quality         | 'high'\|'medium'\|'low' | 'high'  | 绘制画质，影响线条清晰度                                 |
| autoFit         | boolean                 | true    | 是否自适应缩放图片                                       |

---

## 4. 事件（Emits）

| 事件名 | 参数类型                          | 说明                       |
| ------ | --------------------------------- | -------------------------- |
| loaded | { width: number; height: number } | 图片加载成功，返回原始尺寸 |
| error  | string                            | 图片加载失败，返回错误信息 |

---

## 5. 交互说明

- **加载中**：图片加载时显示 loading 动画
- **加载失败**：图片加载失败时显示错误提示
- **无签名**：未传入图片时显示“无签名”提示
- **点击图片**：弹出大图预览（n-modal，最大宽 600px）
- **高分屏适配**：自动适配 devicePixelRatio，保证清晰度

---

## 6. UI 规范

- **组件库**：Naive UI
- **布局**：居中展示，canvas 区域自适应容器宽高
- **样式**：Less + scoped，风格与项目统一
- **状态提示**：使用 n-spin、n-text 组件
- **预览弹窗**：n-modal，最大宽度 600px

---

## 7. 用法示例

```vue
<template>
  <signature-image
    :signature-base64="userSignature"
    :width="200"
    :height="80"
    background-color="#f8f8f8"
    border-color="#d0d0d0"
    :show-border="true"
    quality="high"
    @loaded="onLoaded"
    @error="onError"
  />
</template>

<script setup lang="ts">
import SignatureImage from './signature-image.vue';
import { ref } from 'vue';

const userSignature = ref('...base64数据...');
const onLoaded = (dim) => {
  console.log('签名原始尺寸', dim);
};
const onError = (msg) => {
  window.$message.error(msg);
};
</script>
```

---

## 8. 关键实现说明

- 使用 canvas 绘制，支持多次叠加增强线条清晰度
- 支持高分屏缩放，自动适配容器尺寸
- 预览弹窗内 canvas 按原始尺寸或最大 500x300 绘制
- 支持自定义背景色、边框色、画质
- 所有状态均有友好提示

---

## 9. 维护与扩展建议

- 如需支持更多图片格式，可扩展 base64 前缀识别
- 预览弹窗样式、最大尺寸可根据业务调整
- 事件类型、props 可根据实际需求扩展

---

> 本文档为 signature-image 组件说明，适用于 Vue3 + Naive UI 项目，后续如有变更请及时同步更新。 