import { defineConfig } from 'vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver, VantResolver } from 'unplugin-vue-components/resolvers';
import { viteCommonjs } from '@originjs/vite-plugin-commonjs';
import { htmlTransform } from './src/utils/vite/html-transform';
import { rem, remPreset } from 'vite-plugin-fz';
import baseConfig from './src/config/base';
import {
    AutoImportResolvers,
    AutoComponentsResolvers,
    AutoImportBusinessPreset
} from './src/utils/vite/auto-resolvers';
import legacy from '@vitejs/plugin-legacy';
import vueSetupExtend from 'vite-plugin-vue-setup-extend-plus';
import preprocessorPreset from 'wp-preprocessor/dist/preset';
import requestPreset from 'wp-request/dist/preset';
import AutoApi from 'vitejs-plugin-api-auto-import';
import UnoCSS from 'unocss/vite';
import HtmlSecurity from 'vite-plugin-html-security';
import path from 'path';
import md5 from 'md5';
import VueDevTools from 'vite-plugin-vue-devtools';
import { execSync } from 'child_process';
import dayjs from 'dayjs';
import GetVueRef from 'vitejs-get-vue-ref';
// 获取git信息
const getGitInfo = () => {
    try {
        const hash = execSync('git rev-parse --short HEAD').toString().trim();
        const user = execSync('git config user.name').toString().trim();
        const date = dayjs().format('YYYY-MM-DD HH:mm:ss');
        return {
            hash,
            user,
            date
        };
    } catch (e) {
        return {
            hash: 'develop',
            user: 'unknown',
            date: '2025-01-01 00:00:00'
        };
    }
};

// https://vitejs.dev/config/
export default defineConfig({
    base: baseConfig.base,
    define: {
        __GIT_INFO__: getGitInfo()
    },
    plugins: [
        UnoCSS(),
        vue(),
        createSvgIconsPlugin({
            iconDirs: [path.resolve(process.cwd(), 'src/icons')],
            symbolId: 'icon-[dir]-[name]',
            svgoOptions: {
                full: true
            }
        }),
        vueJsx(),
        viteCommonjs(),
        vueSetupExtend(),
        AutoImport({
            include: [
                /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
                /\.vue$/,
                /\.vue\?vue/, // .vue
                /\.md$/ // .md
            ],
            imports: [
                preprocessorPreset,
                requestPreset,
                remPreset,
                {
                    '@/api': ['api']
                },
                AutoImportBusinessPreset(),
                // presets
                'vue',
                'vue-router',
                '@vueuse/core',
                'pinia'
            ],
            eslintrc: {
                enabled: true
            },
            resolvers: [AutoImportResolvers()],
            dts: true,
            dirs: []
        }),
        Components({
            resolvers: [NaiveUiResolver(), VantResolver(), AutoComponentsResolvers()]
        }),
        htmlTransform(),
        rem(),
        legacy({
            targets: ['defaults', 'not IE 11'],
            /**
             * For chrome >= 61
             * global-this is vaild from chrome 70
             */
            modernPolyfills: ['es.global-this', 'es.array.flat']
        }),
        AutoApi({
            resolveAliasName: '@/api/apis',
            dir: 'src/api/apis'
        }),
        AutoApi({
            name: '$alert',
            resolveAliasName: '@/alert',
            dir: 'src/alert'
        }),
        AutoApi({
            name: '$utils',
            resolveAliasName: '@/utils/utils',
            dir: 'src/utils/utils'
        }),
        HtmlSecurity({ outputDir: 'dist_management' }),
        VueDevTools({
            launchEditor: process.env.trae ? 'trae' : process.env.cursor ? 'cursor' : 'code'
        }),
        AutoApi({
            name: '$hooks',
            resolveAliasName: '@/hooks',
            dir: 'src/hooks'
        }),
        AutoApi({
            name: '$datas',
            resolveAliasName: '@/data',
            dir: 'src/data'
        }),
        GetVueRef()
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src')
        }
    },

    build: {
        target: 'es2015',
        chunkSizeWarningLimit: 5000,
        assetsInlineLimit: 0,
        outDir: 'dist_management',
        rollupOptions: {
            output: !baseConfig.filenameHash
                ? {}
                : {
                      chunkFileNames: (chunkInfo) => {
                          const newFileName = md5(
                              (chunkInfo.facadeModuleId || chunkInfo.name)
                                  .replace(__dirname, '')
                                  .replace(/\/|\\|\./g, '-')
                          );
                          return `assets/chunk-${newFileName.slice(0, 8)}.js`;
                      },
                      assetFileNames(chunkInfo) {
                          const newFileName = md5(
                              (chunkInfo.name || '').replace(__dirname, '').replace(/\/|\\|\./g, '-')
                          );
                          return `assets/${newFileName.slice(0, 8)}.[ext]`;
                      },
                      entryFileNames(chunkInfo) {
                          const newFileName = md5(
                              (chunkInfo.facadeModuleId || chunkInfo.name)
                                  .replace(__dirname, '')
                                  .replace(/\/|\\|\./g, '-')
                          );
                          return `assets/entry-${newFileName.slice(0, 8)}.js`;
                      }
                  }
        }
    },
    server: {
        host: '0.0.0.0',
        hmr: {
            overlay: false
        }
    }
});
